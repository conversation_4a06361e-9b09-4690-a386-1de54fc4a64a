require('dotenv').config();
const express = require('express');
const cors = require('cors');
const { Pool } = require('pg');

const app = express();

// Middleware
app.use(cors());
app.use(express.json());

// Configuration de la base de données
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'Facturation',
  password: process.env.DB_PASSWORD || '123456',
  port: process.env.DB_PORT || 5432,
});

// Route de test
app.get('/', (req, res) => {
  res.json({ 
    message: 'Serveur Facturation fonctionnel', 
    timestamp: new Date().toISOString() 
  });
});

// Route de connexion
app.post('/login', async (req, res) => {
  console.log('📥 Requête de connexion reçue:', req.body);
  const { email, motDepass } = req.body;

  try {
    // Validation des champs requis
    if (!email || !motDepass) {
      return res.status(400).json({
        success: false,
        message: "Email et mot de passe requis"
      });
    }

    console.log('🔍 Recherche de l\'utilisateur:', email);
    
    // Recherche de l'utilisateur
    const userResult = await pool.query(
      'SELECT * FROM Utilisateur WHERE email = $1', 
      [email]
    );
    
    if (userResult.rows.length === 0) {
      console.log('❌ Aucun utilisateur trouvé avec cet email:', email);
      return res.status(401).json({ 
        success: false,
        message: 'Email ou mot de passe incorrect' 
      });
    }

    const user = userResult.rows[0];
    console.log('✅ Utilisateur trouvé:', { 
      email: user.email, 
      role: user.role 
    });

    // Vérification du mot de passe
    if (motDepass !== user.motdepass) {
      console.log('❌ Mot de passe incorrect pour:', email);
      return res.status(401).json({ 
        success: false,
        message: 'Email ou mot de passe incorrect' 
      });
    }

    // Connexion réussie
    console.log('✅ Connexion réussie pour:', email, '- Rôle:', user.role);
    res.json({
      success: true,
      message: 'Connexion réussie',
      user: {
        id: user.idtech,
        nom: user.nom,
        prenom: user.prenom,
        email: user.email,
        role: user.role
      }
    });

  } catch (error) {
    console.error('❌ Erreur de connexion:', error);
    res.status(500).json({ 
      success: false,
      message: 'Erreur serveur',
      error: error.message 
    });
  }
});

// Démarrer le serveur
const PORT = process.env.PORT || 3001;

app.listen(PORT, () => {
  console.log(`🚀 Serveur Facturation démarré sur http://localhost:${PORT}`);
  console.log(`📊 Base de données: ${process.env.DB_NAME || 'Facturation'}`);
  console.log('🔑 Comptes disponibles:');
  console.log('  - <EMAIL> / admin123 (Admin)');
  console.log('  - <EMAIL> / Tech123 (Tech)');
});
