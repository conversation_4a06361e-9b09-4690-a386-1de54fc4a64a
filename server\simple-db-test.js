const { Pool } = require('pg');

console.log('🔍 Test simple de connexion à la base de données...');

const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'Facutration',
  password: '123456',
  port: 5432,
});

async function testConnection() {
  try {
    console.log('Tentative de connexion...');
    const client = await pool.connect();
    console.log('✅ Connexion réussie !');
    
    const result = await client.query('SELECT NOW()');
    console.log('⏰ Heure serveur:', result.rows[0].now);
    
    client.release();
    await pool.end();
    console.log('✅ Test terminé avec succès');
  } catch (error) {
    console.error('❌ Erreur de connexion:', error.message);
    console.error('Code d\'erreur:', error.code);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 Solutions possibles:');
      console.log('1. Vérifiez que PostgreSQL est démarré');
      console.log('2. Vérifiez que la base "Facutration" existe');
      console.log('3. Vérifiez les paramètres de connexion');
    }
  }
}

testConnection();
