// Test simple de l'API clients
async function testClientsAPI() {
  console.log('🧪 Test de l\'API clients...');
  
  try {
    // Test de la route principale
    console.log('\n1. Test de la route principale...');
    const mainResponse = await fetch('http://localhost:3003/');
    const mainData = await mainResponse.json();
    console.log('✅ Route principale:', mainData.message);
    
    // Test de la route clients
    console.log('\n2. Test de la route clients...');
    const clientsResponse = await fetch('http://localhost:3003/api/clients');
    
    if (!clientsResponse.ok) {
      throw new Error(`Erreur HTTP: ${clientsResponse.status}`);
    }
    
    const clientsData = await clientsResponse.json();
    console.log('✅ Route clients:', clientsData.message);
    console.log(`📊 Nombre de clients: ${clientsData.count}`);
    
    if (clientsData.data && clientsData.data.length > 0) {
      console.log('\n👥 Exemples de clients:');
      clientsData.data.slice(0, 3).forEach(client => {
        console.log(`   - ${client.nom} ${client.prenom} (${client.ville})`);
      });
    }
    
    console.log('\n✅ Tous les tests sont passés !');
    
  } catch (error) {
    console.error('❌ Erreur lors du test:', error.message);
    
    if (error.message.includes('ECONNREFUSED')) {
      console.log('\n💡 Solutions:');
      console.log('1. Vérifiez que le serveur est démarré: node server/test-clients.js');
      console.log('2. Vérifiez que le port 3003 est libre');
    }
  }
}

testClientsAPI();
