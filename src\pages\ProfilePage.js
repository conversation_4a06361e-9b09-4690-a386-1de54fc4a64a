import React, { useState } from 'react';
import '../TechnicianDashboard.css';

const ProfilePage = ({ user, onBack, onLogout }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [profileData, setProfileData] = useState({
    prenom: user?.prenom || 'Technicien',
    nom: user?.nom || 'Facturation',
    email: user?.email || '<EMAIL>',
    telephone: user?.telephone || '+216 20 123 456',
    adresse: user?.adresse || 'Tunis, Tunisie',
    dateEmbauche: user?.dateEmbauche || '2024-01-01'
  });

  const handleSave = () => {
    // Ici vous pourriez envoyer les données au serveur
    setIsEditing(false);
    alert('Profil mis à jour avec succès !');
  };

  const handleCancel = () => {
    // Restaurer les données originales
    setProfileData({
      prenom: user?.prenom || 'Technicien',
      nom: user?.nom || 'Facturation',
      email: user?.email || '<EMAIL>',
      telephone: user?.telephone || '+216 20 123 456',
      adresse: user?.adresse || 'Tunis, Tunisie',
      dateEmbauche: user?.dateEmbauche || '2024-01-01'
    });
    setIsEditing(false);
  };

  const handleInputChange = (field, value) => {
    setProfileData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <div className="tech-mobile-content">
      <div className="tech-mobile-card">
        <div className="tech-mobile-card-header">
          <button onClick={onBack} className="tech-mobile-back-btn">
            ← Retour
          </button>
          <div>
            <h1 className="tech-mobile-card-title">Mon Profil</h1>
            <p className="tech-mobile-card-subtitle">Gestion des informations personnelles</p>
          </div>
        </div>
      </div>

      {/* Photo de profil et informations principales */}
      <div className="tech-mobile-card">
        <div className="tech-mobile-profile-card">
          <div className="tech-mobile-profile-avatar">
            {profileData.prenom?.charAt(0) || 'T'}
          </div>
          <div className="tech-mobile-profile-info">
            <h2 className="tech-mobile-profile-name">
              {profileData.prenom} {profileData.nom}
            </h2>
            <p className="tech-mobile-profile-role">Technicien</p>
            <p className="tech-mobile-profile-email">{profileData.email}</p>
          </div>
          <button
            onClick={() => setIsEditing(!isEditing)}
            className={`tech-mobile-action-btn ${isEditing ? 'start' : 'complete'}`}
          >
            {isEditing ? '✏️ Édition' : '📝 Modifier'}
          </button>
        </div>
      </div>

      {/* Informations détaillées */}
      <div className="tech-mobile-card">
        <div className="tech-mobile-card-header">
          <h2 className="tech-mobile-card-title">Informations Personnelles</h2>
        </div>
        <div className="tech-mobile-form">
          <div className="tech-mobile-form-group">
            <label>Prénom</label>
            {isEditing ? (
              <input
                type="text"
                value={profileData.prenom}
                onChange={(e) => handleInputChange('prenom', e.target.value)}
                className="tech-mobile-form-input"
              />
            ) : (
              <div className="tech-mobile-profile-field">{profileData.prenom}</div>
            )}
          </div>

          <div className="tech-mobile-form-group">
            <label>Nom</label>
            {isEditing ? (
              <input
                type="text"
                value={profileData.nom}
                onChange={(e) => handleInputChange('nom', e.target.value)}
                className="tech-mobile-form-input"
              />
            ) : (
              <div className="tech-mobile-profile-field">{profileData.nom}</div>
            )}
          </div>

          <div className="tech-mobile-form-group">
            <label>Email</label>
            {isEditing ? (
              <input
                type="email"
                value={profileData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                className="tech-mobile-form-input"
              />
            ) : (
              <div className="tech-mobile-profile-field">{profileData.email}</div>
            )}
          </div>

          <div className="tech-mobile-form-group">
            <label>Téléphone</label>
            {isEditing ? (
              <input
                type="tel"
                value={profileData.telephone}
                onChange={(e) => handleInputChange('telephone', e.target.value)}
                className="tech-mobile-form-input"
              />
            ) : (
              <div className="tech-mobile-profile-field">{profileData.telephone}</div>
            )}
          </div>

          <div className="tech-mobile-form-group">
            <label>Adresse</label>
            {isEditing ? (
              <input
                type="text"
                value={profileData.adresse}
                onChange={(e) => handleInputChange('adresse', e.target.value)}
                className="tech-mobile-form-input"
              />
            ) : (
              <div className="tech-mobile-profile-field">{profileData.adresse}</div>
            )}
          </div>

          <div className="tech-mobile-form-group">
            <label>Date d'embauche</label>
            <div className="tech-mobile-profile-field">
              {new Date(profileData.dateEmbauche).toLocaleDateString('fr-FR')}
            </div>
          </div>

          {isEditing && (
            <div className="tech-mobile-intervention-actions" style={{ marginTop: '20px' }}>
              <button
                onClick={handleCancel}
                className="tech-mobile-action-btn start"
              >
                Annuler
              </button>
              <button
                onClick={handleSave}
                className="tech-mobile-action-btn complete"
              >
                Sauvegarder
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Statistiques du technicien */}
      <div className="tech-mobile-card">
        <div className="tech-mobile-card-header">
          <h2 className="tech-mobile-card-title">📊 Mes Statistiques</h2>
        </div>
        <div className="tech-mobile-stats-grid">
          <div className="tech-mobile-stat-card">
            <div className="tech-mobile-stat-icon">🔧</div>
            <div className="tech-mobile-stat-number">42</div>
            <div className="tech-mobile-stat-label">Interventions</div>
          </div>
          <div className="tech-mobile-stat-card">
            <div className="tech-mobile-stat-icon">💧</div>
            <div className="tech-mobile-stat-number">156</div>
            <div className="tech-mobile-stat-label">Relevés</div>
          </div>
          <div className="tech-mobile-stat-card">
            <div className="tech-mobile-stat-icon">📱</div>
            <div className="tech-mobile-stat-number">89</div>
            <div className="tech-mobile-stat-label">QR Scannés</div>
          </div>
          <div className="tech-mobile-stat-card">
            <div className="tech-mobile-stat-icon">⭐</div>
            <div className="tech-mobile-stat-number">4.8</div>
            <div className="tech-mobile-stat-label">Note</div>
          </div>
        </div>
      </div>

      {/* Actions du compte */}
      <div className="tech-mobile-card">
        <div className="tech-mobile-card-header">
          <h2 className="tech-mobile-card-title">⚙️ Paramètres du Compte</h2>
        </div>
        <div style={{ padding: '20px' }}>
          <button
            onClick={() => alert('Fonctionnalité de changement de mot de passe à implémenter')}
            className="tech-mobile-action-btn start"
            style={{ width: '100%', marginBottom: '10px' }}
          >
            🔒 Changer le mot de passe
          </button>
          <button
            onClick={() => alert('Fonctionnalité de notification à implémenter')}
            className="tech-mobile-action-btn start"
            style={{ width: '100%', marginBottom: '10px' }}
          >
            🔔 Paramètres de notification
          </button>
          <button
            onClick={() => {
              if (window.confirm('Êtes-vous sûr de vouloir vous déconnecter ?')) {
                onLogout();
              }
            }}
            className="tech-mobile-action-btn start"
            style={{ 
              width: '100%', 
              backgroundColor: '#dc2626',
              borderColor: '#dc2626'
            }}
          >
            🚪 Se déconnecter
          </button>
        </div>
      </div>
    </div>
  );
};

export default ProfilePage;
