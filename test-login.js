// Test de la route de login
async function testLogin() {
  console.log('🧪 Test de la route de login');
  console.log('==============================');
  
  const tests = [
    {
      name: 'Connexion Technicien',
      email: '<EMAIL>',
      motDepass: 'Tech123',
      expectedRole: 'Tech'
    },
    {
      name: 'Connexion Admin',
      email: '<EMAIL>',
      motDepass: 'admin123',
      expectedRole: 'Admin'
    },
    {
      name: 'Identifiants incorrects',
      email: '<EMAIL>',
      motDepass: 'wrongpass',
      shouldFail: true
    }
  ];
  
  for (const test of tests) {
    try {
      console.log(`\n🔐 Test: ${test.name}`);
      console.log(`   📧 Email: ${test.email}`);
      console.log(`   🔑 Mot de passe: ${test.motDepass}`);
      
      const response = await fetch('http://localhost:3003/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: test.email,
          motDepass: test.motDepass
        })
      });
      
      const data = await response.json();
      
      if (test.shouldFail) {
        if (!response.ok && !data.success) {
          console.log(`   ✅ Échec attendu: ${data.message}`);
        } else {
          console.log(`   ❌ Devrait échouer mais a réussi`);
        }
      } else {
        if (response.ok && data.success) {
          console.log(`   ✅ Connexion réussie`);
          console.log(`   👤 Utilisateur: ${data.user.nom} ${data.user.prenom}`);
          console.log(`   🎭 Rôle: ${data.user.role}`);
          
          if (data.user.role === test.expectedRole) {
            console.log(`   ✅ Rôle correct: ${test.expectedRole}`);
          } else {
            console.log(`   ❌ Rôle incorrect. Attendu: ${test.expectedRole}, Reçu: ${data.user.role}`);
          }
        } else {
          console.log(`   ❌ Connexion échouée: ${data.message}`);
        }
      }
      
    } catch (error) {
      console.log(`   ❌ Erreur: ${error.message}`);
      
      if (error.message.includes('ECONNREFUSED')) {
        console.log(`   💡 Solution: Démarrer le serveur avec "node server/test-clients.js"`);
      }
    }
  }
  
  console.log('\n🎯 Test terminé !');
  console.log('\n📋 Instructions pour tester dans l\'application:');
  console.log('1. Ouvrir http://localhost:3000');
  console.log('2. Utiliser <EMAIL> / Tech123');
  console.log('3. Cliquer sur "Se Connecter"');
  console.log('4. Vous devriez être redirigé vers le dashboard technicien');
}

testLogin();
