import React, { useState, useEffect } from 'react';
import '../TechnicianDashboard.css';

const OverviewPage = ({ user, onNavigate }) => {
  const [stats, setStats] = useState({
    interventions: 0,
    interventionsEnCours: 0,
    clients: 0,
    relevesAujourdhui: 0
  });
  const [recentActivities, setRecentActivities] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      
      // Charger les statistiques
      const mockStats = {
        interventions: 42,
        interventionsEnCours: 3,
        clients: 156,
        relevesAujourdhui: 8
      };
      
      // Charger les activités récentes
      const mockActivities = [
        {
          id: 1,
          type: 'consommation',
          title: 'Relevé effectué',
          description: 'Client: Dupont Jean - 125.5 m³',
          time: '10:30',
          icon: '💧'
        },
        {
          id: 2,
          type: 'scan',
          title: 'QR Code scanné',
          description: 'Compteur CPT002 identifié',
          time: '09:45',
          icon: '📱'
        },
        {
          id: 3,
          type: 'localisation',
          title: 'Client localisé',
          description: 'Ben Ali Ahmed - GPS ouvert',
          time: '09:15',
          icon: '🗺️'
        },
        {
          id: 4,
          type: 'intervention',
          title: 'Intervention planifiée',
          description: 'Maintenance compteur - 14h00',
          time: '08:30',
          icon: '🔧'
        }
      ];

      setStats(mockStats);
      setRecentActivities(mockActivities);
    } catch (error) {
      console.error('Erreur lors du chargement des données:', error);
    } finally {
      setLoading(false);
    }
  };

  const quickActions = [
    {
      id: 'scan',
      title: 'Scanner QR',
      description: 'Identifier un compteur',
      icon: '📱',
      color: '#6366f1'
    },
    {
      id: 'consommation',
      title: 'Nouveau Relevé',
      description: 'Saisir consommation',
      icon: '💧',
      color: '#059669'
    },
    {
      id: 'clients',
      title: 'CLIENTS →',
      description: 'Liste des clients',
      icon: '👥',
      color: '#6c5ce7'
    },
    {
      id: 'map',
      title: 'Localisation',
      description: 'Carte des clients',
      icon: '🗺️',
      color: '#7c3aed'
    }
  ];

  if (loading) {
    return (
      <div className="tech-mobile-content">
        <div className="tech-mobile-card">
          <div className="tech-mobile-loading">
            <div className="tech-mobile-spinner"></div>
            <p>Chargement du tableau de bord...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="tech-mobile-content">
      {/* En-tête de bienvenue */}
      <div className="tech-mobile-card">
        <div className="tech-mobile-card-header">
          <div>
            <h1 className="tech-mobile-card-title">
              Bonjour {user?.prenom || 'Technicien'} ! 👋
            </h1>
            <p className="tech-mobile-card-subtitle">
              {new Date().toLocaleDateString('fr-FR', { 
                weekday: 'long', 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
              })}
            </p>
          </div>
        </div>
      </div>

      {/* Statistiques principales */}
      <div className="tech-mobile-stats-grid">
        <div className="tech-mobile-stat-card">
          <div className="tech-mobile-stat-icon">🔧</div>
          <div className="tech-mobile-stat-number">{stats.interventions}</div>
          <div className="tech-mobile-stat-label">Interventions</div>
        </div>

        <div className="tech-mobile-stat-card">
          <div className="tech-mobile-stat-icon">⏳</div>
          <div className="tech-mobile-stat-number">{stats.interventionsEnCours}</div>
          <div className="tech-mobile-stat-label">En cours</div>
        </div>

        <div className="tech-mobile-stat-card">
          <div className="tech-mobile-stat-icon">👥</div>
          <div className="tech-mobile-stat-number">{stats.clients}</div>
          <div className="tech-mobile-stat-label">Clients</div>
        </div>

        <div className="tech-mobile-stat-card">
          <div className="tech-mobile-stat-icon">💧</div>
          <div className="tech-mobile-stat-number">{stats.relevesAujourdhui}</div>
          <div className="tech-mobile-stat-label">Relevés aujourd'hui</div>
        </div>
      </div>

      {/* Actions rapides */}
      <div className="tech-mobile-card">
        <div className="tech-mobile-card-header">
          <h2 className="tech-mobile-card-title">⚡ Actions Rapides</h2>
        </div>
        <div className="tech-mobile-quick-actions">
          {quickActions.map(action => (
            <button
              key={action.id}
              className="tech-mobile-quick-action"
              onClick={() => onNavigate(action.id)}
              style={{ borderColor: action.color }}
            >
              <div 
                className="tech-mobile-quick-action-icon"
                style={{ backgroundColor: action.color }}
              >
                {action.icon}
              </div>
              <div className="tech-mobile-quick-action-content">
                <div className="tech-mobile-quick-action-title">{action.title}</div>
                <div className="tech-mobile-quick-action-desc">{action.description}</div>
              </div>
            </button>
          ))}
        </div>
      </div>

      {/* Activités récentes */}
      <div className="tech-mobile-card">
        <div className="tech-mobile-card-header">
          <h2 className="tech-mobile-card-title">📋 Activités Récentes</h2>
          <button
            onClick={() => onNavigate('historique')}
            className="tech-mobile-action-btn start"
            style={{ fontSize: '12px', padding: '6px 12px' }}
          >
            Voir tout
          </button>
        </div>
      </div>

      {recentActivities.map(activity => (
        <div key={activity.id} className="tech-mobile-intervention-item">
          <div className="tech-mobile-intervention-header">
            <div className="tech-mobile-intervention-client">
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <span style={{ fontSize: '20px' }}>{activity.icon}</span>
                <strong>{activity.title}</strong>
              </div>
              <div style={{ fontSize: '14px', color: '#6b7280', marginTop: '2px' }}>
                {activity.description}
              </div>
            </div>
            <div className="tech-mobile-intervention-badge">
              {activity.time}
            </div>
          </div>
        </div>
      ))}

      {/* Météo et informations utiles */}
      <div className="tech-mobile-card">
        <div className="tech-mobile-card-header">
          <h2 className="tech-mobile-card-title">ℹ️ Informations Utiles</h2>
        </div>
        <div style={{ padding: '20px' }}>
          <div style={{ 
            background: 'linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%)',
            color: 'white',
            padding: '15px',
            borderRadius: '12px',
            marginBottom: '15px'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
              <span style={{ fontSize: '24px' }}>🌤️</span>
              <div>
                <div style={{ fontWeight: '600' }}>Météo: Ensoleillé, 24°C</div>
                <div style={{ fontSize: '14px', opacity: '0.9' }}>
                  Conditions idéales pour les interventions extérieures
                </div>
              </div>
            </div>
          </div>
          
          <div style={{ 
            background: '#f0f9ff',
            border: '1px solid #0ea5e9',
            padding: '15px',
            borderRadius: '12px',
            color: '#0c4a6e'
          }}>
            <div style={{ fontWeight: '600', marginBottom: '5px' }}>💡 Conseil du jour</div>
            <div style={{ fontSize: '14px' }}>
              N'oubliez pas de vérifier l'état des compteurs lors de vos relevés et de signaler toute anomalie.
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OverviewPage;
