const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 3003;

// Middleware
app.use(cors());
app.use(express.json());

console.log('🚀 Démarrage du serveur de test...');

// Route de test simple
app.get('/', (req, res) => {
  console.log('📡 Route / appelée');
  res.json({
    message: 'Serveur test fonctionnel',
    timestamp: new Date().toISOString(),
    status: 'OK'
  });
});

// Route de test API
app.get('/api/test', (req, res) => {
  console.log('📡 Route /api/test appelée');
  res.json({
    success: true,
    message: 'API fonctionnelle',
    timestamp: new Date().toISOString()
  });
});

// Démarrage du serveur
app.listen(PORT, () => {
  console.log(`✅ Serveur test démarré sur http://localhost:${PORT}`);
  console.log('📡 Routes disponibles:');
  console.log('  - GET / (test)');
  console.log('  - GET /api/test (test API)');
});

// Gestion des erreurs
process.on('uncaughtException', (error) => {
  console.error('❌ Erreur non gérée:', error);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Promesse rejetée:', reason);
});
