require('dotenv').config();
const express = require('express');
const { Pool } = require('pg');
const cors = require('cors');

const app = express();
app.use(express.json());
app.use(cors());

// Configuration de la base de données
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'Facutration', // Nom correct de la base
  password: process.env.DB_PASSWORD || '123456',
  port: process.env.DB_PORT || 5432,
});

// Route de test
app.get('/', (req, res) => {
  res.send('Serveur Facutration fonctionnel');
});

// Route de connexion adaptée à votre table Utilisateur
app.post('/login', async (req, res) => {
  console.log('Requête de connexion reçue:', req.body);
  const { email, motDepass } = req.body;

  try {
    // Validation des champs requis
    if (!email || !motDepass) {
      return res.status(400).json({
        success: false,
        message: "Email et mot de passe requis"
      });
    }

    // Recherche de l'utilisateur dans la table Utilisateur
    const result = await pool.query(
      'SELECT * FROM Utilisateur WHERE email = $1',
      [email]
    );

    if (result.rows.length === 0) {
      console.log('Aucun utilisateur trouvé avec cet email:', email);
      return res.status(401).json({
        success: false,
        message: 'Email ou mot de passe incorrect'
      });
    }

    const user = result.rows[0];
    console.log('Utilisateur trouvé:', { email: user.email, role: user.role });

    // Comparaison directe du mot de passe (en clair dans votre table)
    if (motDepass !== user.motdepass) {
      console.log('Mot de passe incorrect pour:', email);
      return res.status(401).json({
        success: false,
        message: 'Email ou mot de passe incorrect'
      });
    }

    // Connexion réussie
    console.log('Connexion réussie pour:', email);
    res.json({
      success: true,
      message: 'Connexion réussie',
      user: {
        id: user.idtech,
        nom: user.nom,
        prenom: user.prenom,
        email: user.email,
        role: user.role
      }
    });

  } catch (err) {
    console.error('Erreur lors de la connexion:', err);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur',
      error: err.message
    });
  }
});

// Route pour récupérer tous les contrats
app.get('/api/contracts', async (req, res) => {
  try {
    const result = await pool.query(`
      SELECT
        ct.idContract,
        ct.idClient,
        ct.codeQr,
        ct.dateContract,
        ct.marqueCompteur,
        ct.numSerieCompteur,
        ct.posX,
        ct.posY,
        cl.nom,
        cl.prenom,
        cl.adresse,
        cl.ville,
        cl.tel,
        cl.email
      FROM Contract ct
      LEFT JOIN Client cl ON ct.idClient = cl.idClient
      ORDER BY cl.nom, cl.prenom
    `);

    res.json({
      success: true,
      data: result.rows
    });
  } catch (err) {
    console.error('Erreur lors de la récupération des contrats:', err);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// Route pour récupérer toutes les consommations
app.get('/api/consommations', async (req, res) => {
  try {
    const result = await pool.query(`
      SELECT
        c.idCons,
        c.idCont,
        c.consommationPre,
        c.consommationActuelle,
        c.jours,
        c.periode,
        c.idTech,
        c.status,
        ct.codeQr,
        ct.marqueCompteur,
        cl.nom,
        cl.prenom,
        cl.adresse,
        cl.ville
      FROM Consommation c
      LEFT JOIN Contract ct ON c.idCont = ct.idContract
      LEFT JOIN Client cl ON ct.idClient = cl.idClient
      ORDER BY c.idCons DESC
    `);

    res.json({
      success: true,
      data: result.rows
    });
  } catch (err) {
    console.error('Erreur lors de la récupération des consommations:', err);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// Route pour ajouter une nouvelle consommation
app.post('/api/consommations', async (req, res) => {
  try {
    const { idcont, consommationpre, consommationactuelle, jours, periode, idtech, idtranch } = req.body;

    const result = await pool.query(`
      INSERT INTO Consommation (idCont, consommationPre, consommationActuelle, jours, periode, idTech, idTranch, status)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      RETURNING *
    `, [idcont, consommationpre || 0, consommationactuelle, jours || 30, periode, idtech || 1, idtranch || 1, 'nouveau']);

    res.json({
      success: true,
      data: result.rows[0]
    });
  } catch (err) {
    console.error('Erreur lors de l\'ajout de la consommation:', err);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// Route pour récupérer la dernière consommation d'un contrat
app.get('/api/contracts/:idcontract/last-consommation', async (req, res) => {
  try {
    const { idcontract } = req.params;

    const result = await pool.query(`
      SELECT
        c.idCons,
        c.consommationActuelle,
        c.periode,
        c.jours
      FROM Consommation c
      WHERE c.idCont = $1
      ORDER BY c.idCons DESC
      LIMIT 1
    `, [idcontract]);

    if (result.rows.length > 0) {
      res.json({
        success: true,
        data: result.rows[0]
      });
    } else {
      res.json({
        success: true,
        data: null,
        message: 'Aucune consommation précédente trouvée'
      });
    }
  } catch (err) {
    console.error('Erreur lors de la récupération de la dernière consommation:', err);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

const PORT = process.env.PORT || 3003;
app.listen(PORT, () => {
  console.log(`Serveur Facturation démarré sur http://localhost:${PORT}`);
});