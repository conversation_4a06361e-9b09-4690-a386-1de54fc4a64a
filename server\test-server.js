require('dotenv').config();
const express = require('express');
const cors = require('cors');
const { Pool } = require('pg');

const app = express();
const PORT = 3002;

// Middleware
app.use(cors());
app.use(express.json());

// Configuration de la base de données
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'Facturation',
  password: process.env.DB_PASSWORD || '123456',
  port: process.env.DB_PORT || 5432,
});

// Test de connexion à la base de données
async function testConnection() {
  try {
    console.log('🔍 Test de connexion à la base de données...');
    const client = await pool.connect();
    console.log('✅ Connexion à la base de données réussie !');
    
    // Test simple
    const result = await client.query('SELECT NOW()');
    console.log('⏰ Heure actuelle:', result.rows[0].now);
    
    client.release();
    return true;
  } catch (err) {
    console.error('❌ Erreur de connexion à la base de données:', err.message);
    return false;
  }
}

// Route de test
app.get('/', (req, res) => {
  res.json({ 
    message: 'Serveur TechnicianDashboard en cours d\'exécution',
    timestamp: new Date().toISOString(),
    status: 'OK'
  });
});

// Route pour tester la base de données
app.get('/api/test-db', async (req, res) => {
  try {
    const client = await pool.connect();
    const result = await client.query('SELECT COUNT(*) as count FROM utilisateur');
    client.release();
    
    res.json({
      success: true,
      message: 'Connexion à la base de données réussie',
      userCount: result.rows[0].count
    });
  } catch (err) {
    console.error('Erreur DB:', err);
    res.status(500).json({
      success: false,
      message: 'Erreur de connexion à la base de données',
      error: err.message
    });
  }
});

// Route pour lister les tables
app.get('/api/tables', async (req, res) => {
  try {
    const client = await pool.connect();
    const result = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
      ORDER BY table_name
    `);
    client.release();
    
    res.json({
      success: true,
      tables: result.rows.map(row => row.table_name)
    });
  } catch (err) {
    console.error('Erreur tables:', err);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// Démarrage du serveur
async function startServer() {
  console.log('🚀 Démarrage du serveur de test...');
  
  // Test de connexion
  const dbConnected = await testConnection();
  
  if (dbConnected) {
    app.listen(PORT, () => {
      console.log(`✅ Serveur de test démarré sur http://localhost:${PORT}`);
      console.log(`📊 Test DB: http://localhost:${PORT}/api/test-db`);
      console.log(`📋 Tables: http://localhost:${PORT}/api/tables`);
    });
  } else {
    console.log('❌ Impossible de démarrer le serveur - problème de base de données');
    process.exit(1);
  }
}

startServer();
