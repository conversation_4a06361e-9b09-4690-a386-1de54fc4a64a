import React, { useState, useEffect } from 'react';
import '../TechnicianDashboard.css';

const ScannerPage = ({ onBack }) => {
  const [scannerActive, setScannerActive] = useState(false);
  const [scanHistory, setScanHistory] = useState([]);
  const [clients, setClients] = useState([]);

  useEffect(() => {
    fetchClients();
    loadScanHistory();
  }, []);

  const fetchClients = async () => {
    try {
      const response = await fetch('http://localhost:3003/api/clients');
      const data = await response.json();
      setClients(data.clients || []);
    } catch (error) {
      console.error('Erreur lors du chargement des clients:', error);
    }
  };

  const loadScanHistory = () => {
    // Simulation d'historique de scans
    const mockHistory = [
      {
        id: 1,
        qrCode: 'QR001',
        clientNom: '<PERSON><PERSON> Jean',
        compteur: 'CPT001',
        date: new Date().toISOString(),
        resultat: 'Succès'
      },
      {
        id: 2,
        qrCode: 'QR002',
        clientNom: '<PERSON>',
        compteur: 'CPT002',
        date: new Date(Date.now() - 3600000).toISOString(),
        resultat: 'Succès'
      }
    ];
    setScanHistory(mockHistory);
  };

  const handleStartScan = () => {
    setScannerActive(true);
    // Simulation d'un scan après 3 secondes
    setTimeout(() => {
      simulateScan();
    }, 3000);
  };

  const simulateScan = () => {
    // Simulation de scan d'un QR code aléatoire
    const qrCodes = ['QR001', 'QR002', 'QR003', 'QR004', 'QR005'];
    const randomQR = qrCodes[Math.floor(Math.random() * qrCodes.length)];
    
    // Trouver le client correspondant
    const client = clients.find(c => `QR${c.id.toString().padStart(3, '0')}` === randomQR);
    
    if (client) {
      const newScan = {
        id: Date.now(),
        qrCode: randomQR,
        clientNom: client.nom,
        compteur: `CPT${client.id.toString().padStart(3, '0')}`,
        date: new Date().toISOString(),
        resultat: 'Succès'
      };
      
      setScanHistory([newScan, ...scanHistory]);
      alert(`✅ Client identifié !\n\nNom: ${client.nom}\nAdresse: ${client.adresse}\nCompteur: ${newScan.compteur}`);
    } else {
      const newScan = {
        id: Date.now(),
        qrCode: randomQR,
        clientNom: 'Inconnu',
        compteur: 'N/A',
        date: new Date().toISOString(),
        resultat: 'Échec'
      };
      
      setScanHistory([newScan, ...scanHistory]);
      alert('❌ QR Code non reconnu dans la base de données');
    }
    
    setScannerActive(false);
  };

  const handleStopScan = () => {
    setScannerActive(false);
  };

  return (
    <div className="tech-mobile-content">
      <div className="tech-mobile-card">
        <div className="tech-mobile-card-header">
          <button onClick={onBack} className="tech-mobile-back-btn">
            ← Retour
          </button>
          <div>
            <h1 className="tech-mobile-card-title">Scanner QR Code</h1>
            <p className="tech-mobile-card-subtitle">Identification des compteurs</p>
          </div>
        </div>
      </div>

      {/* Interface de scan */}
      <div className="tech-mobile-card">
        {!scannerActive ? (
          <div className="tech-mobile-scanner-placeholder">
            <div className="tech-mobile-scanner-icon">📱</div>
            <h3>Scanner un QR Code</h3>
            <p>Pointez votre appareil vers le QR code du compteur pour identifier le client</p>
            <button
              onClick={handleStartScan}
              className="tech-mobile-action-btn complete"
            >
              Démarrer le scan
            </button>
          </div>
        ) : (
          <div className="tech-mobile-scanner-active">
            <div className="tech-mobile-scanner-viewfinder">
              <div className="tech-mobile-scanner-overlay">
                <div className="tech-mobile-scanner-frame"></div>
              </div>
            </div>
            <p style={{ textAlign: 'center', margin: '20px 0' }}>
              Scan en cours... Positionnez le QR code dans le cadre
            </p>
            <div className="tech-mobile-scanner-actions">
              <button
                onClick={handleStopScan}
                className="tech-mobile-action-btn start"
              >
                Annuler
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Historique des scans */}
      <div className="tech-mobile-card">
        <div className="tech-mobile-card-header">
          <h2 className="tech-mobile-card-title">Historique des Scans</h2>
          <p className="tech-mobile-card-subtitle">{scanHistory.length} scan(s) effectué(s)</p>
        </div>
      </div>

      {scanHistory.length === 0 ? (
        <div className="tech-mobile-card">
          <div className="tech-mobile-empty-state">
            <div className="tech-mobile-empty-icon">📱</div>
            <h3>Aucun scan effectué</h3>
            <p>Commencez par scanner votre premier QR code.</p>
          </div>
        </div>
      ) : (
        scanHistory.map(scan => (
          <div key={scan.id} className="tech-mobile-intervention-item">
            <div className="tech-mobile-intervention-header">
              <div className="tech-mobile-intervention-client">
                <strong>{scan.clientNom}</strong>
                <div style={{ fontSize: '14px', color: '#6b7280', marginTop: '2px' }}>
                  QR: {scan.qrCode}
                </div>
              </div>
              <div 
                className="tech-mobile-intervention-badge"
                style={{ 
                  backgroundColor: scan.resultat === 'Succès' ? '#4caf50' : '#f44336',
                  color: 'white'
                }}
              >
                {scan.resultat}
              </div>
            </div>

            <div className="tech-mobile-intervention-details">
              <div className="tech-mobile-intervention-info">
                <span>🔢 Compteur: {scan.compteur}</span>
              </div>
              <div className="tech-mobile-intervention-info">
                <span>📅 {new Date(scan.date).toLocaleString('fr-FR')}</span>
              </div>
            </div>

            {scan.resultat === 'Succès' && (
              <div className="tech-mobile-intervention-actions">
                <button
                  className="tech-mobile-action-btn start"
                  onClick={() => {
                    const client = clients.find(c => c.nom === scan.clientNom);
                    if (client && client.latitude && client.longitude) {
                      const url = `https://www.google.com/maps?q=${client.latitude},${client.longitude}`;
                      window.open(url, '_blank');
                    } else {
                      alert('Coordonnées GPS non disponibles');
                    }
                  }}
                >
                  Localiser
                </button>
                <button
                  className="tech-mobile-action-btn complete"
                  onClick={() => {
                    alert(`Informations du client:\n\nNom: ${scan.clientNom}\nCompteur: ${scan.compteur}\nQR Code: ${scan.qrCode}`);
                  }}
                >
                  Détails
                </button>
              </div>
            )}
          </div>
        ))
      )}
    </div>
  );
};

export default ScannerPage;
