# Guide d'utilisation - TechnicianDashboard.js

## 🎯 Objectif
Ce serveur Node.js **détecte automatiquement** votre base de données PostgreSQL "Facturation" et **consulte tous les tableaux** qu'il trouve, exactement comme vous l'avez demandé.

## 🚀 Démarrage rapide

### 1. <PERSON><PERSON>marrer le serveur
```bash
node server/TechnicianDashboard.js
```

### 2. Tester le serveur
```bash
node server/test-TechnicianDashboard.js
```

## 🔍 Fonctionnalités de détection automatique

### ✅ Ce que fait le serveur au démarrage :

1. **Se connecte à votre base "Facturation"**
2. **Détecte automatiquement toutes les tables** présentes
3. **Analyse le schéma** de chaque table (colonnes, types, etc.)
4. **Compte les enregistrements** dans chaque table
5. **Affiche un rapport complet** dans la console

### 📊 Exemple de sortie au démarrage :
```
🚀 Initialisation du serveur TechnicianDashboard...
✅ Connexion à la base de données réussie
🔍 Détection des tables de la base de données...
✅ 7 tables détectées: [ 'client', 'consommation', 'contract', 'facture', 'secteur', 'tranch', 'utilisateur' ]

📋 Table: client
   - idclient (integer)
   - nom (character varying)
   - prenom (character varying)
   - adresse (text)
   - ville (character varying)
   - tel (character varying)
   - email (character varying)
   - ids (integer)

📊 Comptage des enregistrements par table...
   - client: 15 enregistrement(s)
   - consommation: 45 enregistrement(s)
   - contract: 15 enregistrement(s)
   - facture: 30 enregistrement(s)
   - secteur: 5 enregistrement(s)
   - tranch: 3 enregistrement(s)
   - utilisateur: 8 enregistrement(s)
```

## 🌐 Routes API disponibles

### 1. **GET /api/database/schema** - Schéma complet
Retourne toutes les tables détectées avec leur structure complète
```json
{
  "success": true,
  "data": {
    "tables": ["client", "consommation", "contract", ...],
    "schema": { ... },
    "counts": { "client": 15, "consommation": 45, ... },
    "total_tables": 7
  }
}
```

### 2. **GET /api/table/:tableName** - Consultation dynamique
Consulte n'importe quelle table détectée
```bash
# Exemples d'utilisation :
GET /api/table/client
GET /api/table/consommation
GET /api/table/facture
GET /api/table/utilisateur
```

### 3. **POST /api/query** - Requêtes SQL personnalisées
Exécute des requêtes SELECT personnalisées
```json
{
  "query": "SELECT * FROM client WHERE ville = $1",
  "params": ["Paris"]
}
```

### 4. **GET /api/technician/dashboard/:techId** - Dashboard technicien
Données complètes pour un technicien spécifique

### 5. **GET /api/scan/:qrCode** - Scanner QR
Recherche un contrat par son QR code

## 💻 Utilisation avec votre React

### Remplacer les données statiques par des données dynamiques :

```javascript
// Dans votre composant React
import { useState, useEffect } from 'react';

function ListesClients() {
  const [clients, setClients] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Récupérer les clients depuis la base de données
    fetch('http://localhost:3002/api/table/client')
      .then(res => res.json())
      .then(data => {
        if (data.success) {
          setClients(data.data);
        }
        setLoading(false);
      })
      .catch(error => {
        console.error('Erreur:', error);
        setLoading(false);
      });
  }, []);

  if (loading) return <div>Chargement...</div>;

  return (
    <div>
      <h2>Liste des Clients ({clients.length})</h2>
      {clients.map(client => (
        <div key={client.idclient}>
          <h3>{client.nom} {client.prenom}</h3>
          <p>{client.adresse}, {client.ville}</p>
          <p>Tel: {client.tel}</p>
          <p>Email: {client.email}</p>
        </div>
      ))}
    </div>
  );
}
```

### Récupérer le schéma complet de la base :

```javascript
// Obtenir toutes les tables disponibles
fetch('http://localhost:3002/api/database/schema')
  .then(res => res.json())
  .then(data => {
    console.log('Tables disponibles:', data.data.tables);
    console.log('Schéma complet:', data.data.schema);
    console.log('Nombre d\'enregistrements:', data.data.counts);
  });
```

## 🔧 Configuration

### Variables d'environnement (.env) :
```
DB_USER=postgres
DB_HOST=localhost
DB_NAME=Facturation
DB_PASSWORD=123456
DB_PORT=5432
PORT=3002
```

## 📝 Logs et debugging

Le serveur affiche des logs détaillés :
- 🔍 Détection des tables
- 📊 Comptage des enregistrements
- 📥 Requêtes reçues
- ✅ Succès des opérations
- ❌ Erreurs rencontrées

## 🎯 Avantages de cette approche

1. **Détection automatique** : Pas besoin de configurer manuellement les tables
2. **Flexibilité** : S'adapte automatiquement si vous ajoutez/supprimez des tables
3. **Données dynamiques** : Fini les données statiques, tout vient de votre vraie base
4. **Sécurité** : Seules les requêtes SELECT sont autorisées
5. **Performance** : Pagination automatique pour les grandes tables
6. **Debugging** : Logs complets pour identifier les problèmes

## 🚨 Résolution de problèmes

### Problème : "Erreur de connexion à la base de données"
**Solution** : Vérifiez vos paramètres de connexion dans le fichier `.env`

### Problème : "Aucune table trouvée"
**Solution** : Assurez-vous que votre base "Facturation" contient des tables

### Problème : "Table non trouvée"
**Solution** : Utilisez `GET /api/database/schema` pour voir les tables disponibles

### Problème : "Serveur non accessible"
**Solution** : Vérifiez que le serveur est démarré sur le port 3002

## 📞 Test rapide

1. Démarrez le serveur : `node server/TechnicianDashboard.js`
2. Ouvrez votre navigateur : `http://localhost:3002`
3. Testez le schéma : `http://localhost:3002/api/database/schema`
4. Consultez une table : `http://localhost:3002/api/table/client`

Votre serveur détecte maintenant automatiquement votre base de données et consulte tous les tableaux ! 🎉
