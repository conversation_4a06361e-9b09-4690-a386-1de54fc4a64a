console.log('🚀 Démarrage du test simple...');

const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 3002;

console.log('📦 Express et CORS chargés');

// Middleware
app.use(cors());
app.use(express.json());

console.log('✅ Middleware configuré');

// Middleware de logging
app.use((req, res, next) => {
  console.log(`📥 ${new Date().toISOString()} - ${req.method} ${req.url}`);
  next();
});

// Route de test simple
app.get('/', (req, res) => {
  console.log('✅ Route / appelée');
  res.json({
    message: 'Serveur Test Simple fonctionnel',
    timestamp: new Date().toISOString(),
    status: 'OK'
  });
});

// Route de test pour les clients (données statiques)
app.get('/api/table/client', (req, res) => {
  console.log('👥 Route clients appelée');
  res.json({
    success: true,
    data: [
      { idclient: 1, nom: 'Test', prenom: 'Client', email: '<EMAIL>' },
      { idclient: 2, nom: 'Autre', prenom: 'Client', email: '<EMAIL>' }
    ],
    count: 2
  });
});

// Route de test pour les consommations (données vides)
app.get('/api/table/consommation', (req, res) => {
  console.log('💧 Route consommations appelée');
  res.json({
    success: true,
    data: [],
    count: 0
  });
});

// Route de test pour les factures (erreur 404)
app.get('/api/table/facture', (req, res) => {
  console.log('📄 Route factures appelée - retour 404');
  res.status(404).json({
    success: false,
    error: 'not_found',
    message: 'Table facture non trouvée',
    available_tables: ['client', 'consommation', 'contract', 'secteur', 'tranch', 'utilisateur']
  });
});

// Gestion des erreurs
app.use((error, req, res, next) => {
  console.error('❌ Erreur serveur:', error);
  res.status(500).json({
    success: false,
    error: 'Erreur interne du serveur'
  });
});

// Démarrage du serveur
app.listen(PORT, () => {
  console.log('🎉 Serveur Test Simple démarré avec succès !');
  console.log(`📡 URL: http://localhost:${PORT}`);
  console.log('');
  console.log('📋 Routes de test disponibles:');
  console.log('  - GET  / (test serveur)');
  console.log('  - GET  /api/table/client (clients test)');
  console.log('  - GET  /api/table/consommation (consommations vides)');
  console.log('  - GET  /api/table/facture (erreur 404)');
  console.log('');
  console.log('💡 Testez ces URLs dans votre navigateur ou avec curl');
  console.log('🔄 Le serveur est prêt à recevoir des requêtes');
});

console.log('📝 Script de test simple chargé, démarrage du serveur...');
