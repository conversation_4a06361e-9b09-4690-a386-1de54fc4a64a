require('dotenv').config();
const { Pool } = require('pg');

// Configuration de la base de données
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'Facturation',
  password: process.env.DB_PASSWORD || '123456',
  port: process.env.DB_PORT || 5432,
});

async function addTestData() {
  console.log('🔄 Ajout de données de test...');
  
  try {
    const client = await pool.connect();
    
    // Vérifier si des données existent déjà
    const existingClients = await client.query('SELECT COUNT(*) FROM client');
    const clientCount = parseInt(existingClients.rows[0].count);
    
    if (clientCount > 0) {
      console.log(`ℹ️  ${clientCount} clients existent déjà dans la base`);
      console.log('❓ Voulez-vous ajouter plus de données de test ? (Ctrl+C pour annuler)');
    }

    console.log('\n📝 Ajout de clients de test...');
    
    // Ajouter des clients de test
    const clients = [
      {
        nom: 'Dupont',
        prenom: 'Jean',
        adresse: '123 Rue de la Paix',
        ville: 'Tunis',
        tel: '+216 20 123 456',
        email: '<EMAIL>',
        ids: 1
      },
      {
        nom: 'Martin',
        prenom: 'Marie',
        adresse: '456 Avenue Habib Bourguiba',
        ville: 'Sfax',
        tel: '+216 25 789 012',
        email: '<EMAIL>',
        ids: 1
      },
      {
        nom: 'Ben Ali',
        prenom: 'Ahmed',
        adresse: '789 Boulevard de la République',
        ville: 'Sousse',
        tel: '+216 22 345 678',
        email: '<EMAIL>',
        ids: 2
      }
    ];

    for (const clientData of clients) {
      try {
        const result = await client.query(`
          INSERT INTO client (nom, prenom, adresse, ville, tel, email, ids)
          VALUES ($1, $2, $3, $4, $5, $6, $7)
          RETURNING idclient
        `, [clientData.nom, clientData.prenom, clientData.adresse, clientData.ville, clientData.tel, clientData.email, clientData.ids]);

        console.log(`✅ Client ajouté: ${clientData.prenom} ${clientData.nom} (ID: ${result.rows[0].idclient})`);
      } catch (error) {
        if (error.code === '23505') { // Duplicate key
          console.log(`⚠️  Client ${clientData.prenom} ${clientData.nom} existe déjà`);
        } else {
          console.log(`❌ Erreur pour ${clientData.prenom} ${clientData.nom}: ${error.message}`);
        }
      }
    }

    console.log('\n📝 Ajout de contrats de test...');
    
    // Récupérer les IDs des clients
    const clientIds = await client.query('SELECT idclient FROM client LIMIT 3');
    
    if (clientIds.rows.length > 0) {
      const contracts = [
        {
          codeqr: 'QR001234567890',
          datecontract: new Date(),
          idclient: clientIds.rows[0]?.idclient,
          marquecompteur: 'SENSUS',
          numseriecompteur: 'SN123456789',
          posx: '36.8065',
          posy: '10.1815'
        },
        {
          codeqr: 'QR001234567891',
          datecontract: new Date(),
          idclient: clientIds.rows[1]?.idclient,
          marquecompteur: 'ITRON',
          numseriecompteur: 'IT987654321',
          posx: '34.7406',
          posy: '10.7603'
        }
      ];

      for (const contract of contracts) {
        if (contract.idclient) {
          try {
            const result = await client.query(`
              INSERT INTO contract (codeqr, datecontract, idclient, marquecompteur, numseriecompteur, posx, posy)
              VALUES ($1, $2, $3, $4, $5, $6, $7)
              RETURNING idcontract
            `, [contract.codeqr, contract.datecontract, contract.idclient, contract.marquecompteur, contract.numseriecompteur, contract.posx, contract.posy]);
            
            console.log(`✅ Contrat ajouté: ${contract.codeqr} (ID: ${result.rows[0].idcontract})`);
          } catch (error) {
            if (error.code === '23505') {
              console.log(`⚠️  Contrat ${contract.codeqr} existe déjà`);
            } else {
              console.log(`❌ Erreur pour contrat ${contract.codeqr}: ${error.message}`);
            }
          }
        }
      }
    }

    console.log('\n📝 Ajout de tranches de prix...');
    
    // Ajouter des tranches de prix
    const tranches = [
      { prix: 0.180, valeurmin: 0, valeurmax: 20 },
      { prix: 0.280, valeurmin: 21, valeurmax: 30 },
      { prix: 0.320, valeurmin: 31, valeurmax: 50 },
      { prix: 0.395, valeurmin: 51, valeurmax: 999999 }
    ];

    for (const tranche of tranches) {
      try {
        const result = await client.query(`
          INSERT INTO tranch (prix, valeurmin, valeurmax)
          VALUES ($1, $2, $3)
          RETURNING idtranch
        `, [tranche.prix, tranche.valeurmin, tranche.valeurmax]);
        
        console.log(`✅ Tranche ajoutée: ${tranche.valeurmin}-${tranche.valeurmax} m³ à ${tranche.prix} DT (ID: ${result.rows[0].idtranch})`);
      } catch (error) {
        console.log(`⚠️  Tranche ${tranche.valeurmin}-${tranche.valeurmax} existe peut-être déjà`);
      }
    }

    // Compter les enregistrements finaux
    console.log('\n📊 Résumé des données dans la base :');
    const tables = ['client', 'contract', 'tranch', 'secteur', 'utilisateur'];
    
    for (const table of tables) {
      const count = await client.query(`SELECT COUNT(*) FROM ${table}`);
      console.log(`   - ${table}: ${count.rows[0].count} enregistrement(s)`);
    }

    client.release();
    console.log('\n🎉 Données de test ajoutées avec succès !');
    console.log('💡 Vous pouvez maintenant tester votre dashboard avec des données réelles');
    
  } catch (error) {
    console.error('❌ Erreur lors de l\'ajout des données:', error.message);
  } finally {
    await pool.end();
  }
}

// Exécuter l'ajout de données
addTestData();
