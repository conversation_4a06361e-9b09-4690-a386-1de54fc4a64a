require('dotenv').config();
const express = require('express');
const cors = require('cors');
const { Pool } = require('pg');

const app = express();
const PORT = 3003;

// Middleware
app.use(cors());
app.use(express.json());

// Configuration de la base de données
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'Facutration',
  password: process.env.DB_PASSWORD || '123456',
  port: process.env.DB_PORT || 5432,
});

// Route de test simple
app.get('/', (req, res) => {
  res.json({
    message: 'Serveur test clients fonctionnel',
    timestamp: new Date().toISOString(),
    status: 'OK'
  });
});

// Route pour tester la base de données
app.get('/api/test', async (req, res) => {
  try {
    const client = await pool.connect();
    const result = await client.query('SELECT COUNT(*) as count FROM client');
    client.release();
    
    res.json({
      success: true,
      message: 'Test DB réussi',
      clientCount: result.rows[0].count
    });
  } catch (err) {
    console.error('Erreur DB:', err);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// Route de connexion/authentification
app.post('/login', async (req, res) => {
  try {
    console.log('🔐 Tentative de connexion...');
    const { email, motDepass } = req.body;

    console.log('📧 Email:', email);
    console.log('🔑 Mot de passe reçu:', motDepass ? '***' : 'vide');

    // Validation des champs requis
    if (!email || !motDepass) {
      return res.status(400).json({
        success: false,
        message: "Email et mot de passe requis"
      });
    }

    // Test avec les identifiants du technicien
    if (email === '<EMAIL>' && motDepass === 'Tech123') {
      console.log('✅ Connexion réussie pour le technicien');
      return res.json({
        success: true,
        message: 'Connexion réussie',
        user: {
          id: 1,
          nom: 'Technicien',
          prenom: 'Test',
          email: '<EMAIL>',
          role: 'Tech'
        }
      });
    }

    // Test avec les identifiants admin
    if (email === '<EMAIL>' && motDepass === 'admin123') {
      console.log('✅ Connexion réussie pour l\'admin');
      return res.json({
        success: true,
        message: 'Connexion réussie',
        user: {
          id: 2,
          nom: 'Admin',
          prenom: 'Principal',
          email: '<EMAIL>',
          role: 'Admin'
        }
      });
    }

    // Vérifier dans la base de données
    const client = await pool.connect();
    const userQuery = 'SELECT * FROM utilisateur WHERE email = $1';
    const userResult = await client.query(userQuery, [email]);

    if (userResult.rows.length === 0) {
      client.release();
      console.log('❌ Utilisateur non trouvé:', email);
      return res.status(401).json({
        success: false,
        message: 'Email ou mot de passe incorrect'
      });
    }

    const user = userResult.rows[0];
    console.log('👤 Utilisateur trouvé:', { email: user.email, role: user.role });

    // Vérifier le mot de passe
    if (motDepass !== user.motdepass) {
      client.release();
      console.log('❌ Mot de passe incorrect pour:', email);
      return res.status(401).json({
        success: false,
        message: 'Email ou mot de passe incorrect'
      });
    }

    client.release();
    console.log('✅ Connexion réussie pour:', email);

    res.json({
      success: true,
      message: 'Connexion réussie',
      user: {
        id: user.idtech,
        nom: user.nom,
        prenom: user.prenom,
        email: user.email,
        role: user.role
      }
    });

  } catch (error) {
    console.error('❌ Erreur de connexion:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur',
      error: error.message
    });
  }
});

// Route pour récupérer les clients
app.get('/api/clients', async (req, res) => {
  try {
    console.log('📋 Récupération des clients...');
    const client = await pool.connect();

    const query = `
      SELECT
        c.idclient,
        c.nom,
        c.prenom,
        c.adresse,
        c.ville,
        c.tel,
        c.email,
        c.ids,
        s.nom as secteur_nom
      FROM client c
      LEFT JOIN secteur s ON c.ids = s.ids
      ORDER BY c.nom, c.prenom
    `;

    const result = await client.query(query);
    client.release();

    console.log(`✅ ${result.rows.length} clients récupérés`);

    res.json({
      success: true,
      message: 'Clients récupérés avec succès',
      count: result.rows.length,
      data: result.rows
    });
  } catch (err) {
    console.error('❌ Erreur clients:', err);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// Route pour récupérer les contrats avec informations clients
app.get('/api/contracts', async (req, res) => {
  try {
    console.log('📋 Récupération des contrats...');
    const client = await pool.connect();

    const query = `
      SELECT
        co.idcontract,
        co.codeqr,
        co.datecontract,
        co.marquecompteur,
        co.numseriecompteur,
        co.posx,
        co.posy,
        c.idclient,
        c.nom,
        c.prenom,
        c.adresse,
        c.ville,
        c.tel,
        c.email,
        s.nom as secteur_nom
      FROM contract co
      LEFT JOIN client c ON co.idclient = c.idclient
      LEFT JOIN secteur s ON c.ids = s.ids
      ORDER BY c.nom, c.prenom
    `;

    const result = await client.query(query);
    client.release();

    console.log(`✅ ${result.rows.length} contrats récupérés`);

    res.json({
      success: true,
      message: 'Contrats récupérés avec succès',
      count: result.rows.length,
      data: result.rows
    });
  } catch (err) {
    console.error('❌ Erreur contrats:', err);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// Route pour récupérer les consommations
app.get('/api/consommations', async (req, res) => {
  try {
    console.log('📋 Récupération des consommations...');
    const client = await pool.connect();

    const query = `
      SELECT
        cons.idcons,
        cons.consommationpre,
        cons.consommationactuelle,
        cons.jours,
        cons.periode,
        cons.status,
        co.codeqr,
        co.marquecompteur,
        c.nom,
        c.prenom,
        c.adresse,
        u.nom as tech_nom,
        u.prenom as tech_prenom
      FROM consommation cons
      LEFT JOIN contract co ON cons.idcont = co.idcontract
      LEFT JOIN client c ON co.idclient = c.idclient
      LEFT JOIN utilisateur u ON cons.idtech = u.idtech
      ORDER BY cons.idcons DESC
    `;

    const result = await client.query(query);
    client.release();

    console.log(`✅ ${result.rows.length} consommations récupérées`);

    res.json({
      success: true,
      message: 'Consommations récupérées avec succès',
      count: result.rows.length,
      data: result.rows
    });
  } catch (err) {
    console.error('❌ Erreur consommations:', err);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// Route pour ajouter une nouvelle consommation
app.post('/api/consommations', async (req, res) => {
  try {
    console.log('📝 Ajout d\'une nouvelle consommation...');
    const { idcont, consommationpre, consommationactuelle, jours, periode, idtech } = req.body;

    // Validation des données
    if (!idcont || !consommationactuelle) {
      return res.status(400).json({
        success: false,
        error: 'Les champs idcont et consommationactuelle sont obligatoires'
      });
    }

    const client = await pool.connect();

    const query = `
      INSERT INTO consommation (
        consommationpre,
        consommationactuelle,
        idcont,
        idtech,
        idtranch,
        jours,
        periode,
        status
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      RETURNING *
    `;

    const values = [
      consommationpre || 0,
      consommationactuelle,
      idcont,
      idtech || 1, // ID technicien par défaut
      1, // ID tranche par défaut
      jours || 30,
      periode || new Date().toISOString().slice(0, 7),
      'nouveau'
    ];

    const result = await client.query(query, values);
    client.release();

    console.log('✅ Consommation ajoutée:', result.rows[0]);

    res.json({
      success: true,
      message: 'Consommation ajoutée avec succès',
      data: result.rows[0]
    });
  } catch (err) {
    console.error('❌ Erreur ajout consommation:', err);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// Démarrage du serveur
app.listen(PORT, () => {
  console.log(`✅ Serveur test clients démarré sur http://localhost:${PORT}`);
  console.log('📡 Routes:');
  console.log('  - GET / (test)');
  console.log('  - GET /api/test (test DB)');
  console.log('  - POST /login (authentification)');
  console.log('  - GET /api/clients (liste clients)');
  console.log('  - GET /api/contracts (liste contrats avec clients)');
  console.log('  - GET /api/consommations (liste consommations)');
  console.log('  - POST /api/consommations (ajouter consommation)');
  console.log('🔑 Comptes de test:');
  console.log('  - <EMAIL> / Tech123 (Technicien)');
  console.log('  - <EMAIL> / admin123 (Admin)');
});
