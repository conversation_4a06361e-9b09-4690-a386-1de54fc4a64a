const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 3003;

// Middleware
app.use(cors());
app.use(express.json());

// Route de test simple
app.get('/', (req, res) => {
  res.json({ 
    message: 'Serveur test simple fonctionnel', 
    timestamp: new Date().toISOString(),
    port: PORT
  });
});

// Route pour simuler /api/table/client
app.get('/api/table/client', (req, res) => {
  console.log('📥 Requête GET /api/table/client');
  res.json({
    success: true,
    data: [
      { idclient: 1, nom: 'Test', prenom: 'Client1', adresse: 'Rabat', tel: '0612345678' },
      { idclient: 2, nom: 'Test', prenom: 'Client2', adresse: 'Casablanca', tel: '0612345679' }
    ],
    pagination: {
      total: 2,
      limit: 100,
      offset: 0,
      has_more: false
    },
    table_info: {
      name: 'client',
      count: 2
    }
  });
});

// Route pour simuler /api/technician/dashboard/:techId
app.get('/api/technician/dashboard/:techId', (req, res) => {
  const { techId } = req.params;
  console.log(`📥 Requête GET /api/technician/dashboard/${techId}`);
  
  res.json({
    success: true,
    data: {
      technicien: {
        idtech: techId,
        nom: 'Technicien',
        prenom: 'Test',
        email: '<EMAIL>',
        role: 'Tech'
      },
      statistiques: {
        consommations_relevees: 5,
        total_clients: 10,
        dernieres_consommations: [],
        contrats_associes: []
      }
    }
  });
});

// Route pour simuler /api/table/consommation
app.get('/api/table/consommation', (req, res) => {
  console.log('📥 Requête GET /api/table/consommation');
  res.json({
    success: true,
    data: [],
    pagination: {
      total: 0,
      limit: 100,
      offset: 0,
      has_more: false
    },
    table_info: {
      name: 'consommation',
      count: 0
    }
  });
});

// Route pour simuler /api/table/contract
app.get('/api/table/contract', (req, res) => {
  console.log('📥 Requête GET /api/table/contract');
  res.json({
    success: true,
    data: [],
    pagination: {
      total: 0,
      limit: 100,
      offset: 0,
      has_more: false
    },
    table_info: {
      name: 'contract',
      count: 0
    }
  });
});

// Démarrage du serveur
app.listen(PORT, () => {
  console.log(`✅ Serveur test simple démarré sur http://localhost:${PORT}`);
  console.log('📡 Routes simulées:');
  console.log('  - GET / (test)');
  console.log('  - GET /api/table/client');
  console.log('  - GET /api/table/consommation');
  console.log('  - GET /api/table/contract');
  console.log('  - GET /api/technician/dashboard/:techId');
});
