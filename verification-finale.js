// Script de vérification finale pour l'intégration clients
async function verificationFinale() {
  console.log('🔍 Vérification finale de l\'intégration clients');
  console.log('================================================');
  
  const tests = [
    {
      name: 'Connexion serveur principal',
      url: 'http://localhost:3003/',
      expected: 'message'
    },
    {
      name: 'API Clients',
      url: 'http://localhost:3003/api/clients',
      expected: 'data'
    },
    {
      name: 'API Contrats',
      url: 'http://localhost:3003/api/contracts',
      expected: 'data'
    },
    {
      name: 'API Consommations',
      url: 'http://localhost:3003/api/consommations',
      expected: 'data'
    }
  ];
  
  let passedTests = 0;
  let totalTests = tests.length;
  
  for (const test of tests) {
    try {
      console.log(`\n🧪 Test: ${test.name}...`);
      
      const response = await fetch(test.url);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      
      if ((data.success && data[test.expected]) || (test.expected === 'message' && data.message)) {
        console.log(`✅ ${test.name} - OK`);
        
        if (test.expected === 'data' && Array.isArray(data.data)) {
          console.log(`   📊 ${data.data.length} éléments trouvés`);
          
          if (data.data.length > 0) {
            const sample = data.data[0];
            const keys = Object.keys(sample).slice(0, 3);
            console.log(`   🔍 Exemple: ${keys.map(k => `${k}: ${sample[k]}`).join(', ')}`);
          }
        }
        
        passedTests++;
      } else {
        console.log(`❌ ${test.name} - Données manquantes`);
        console.log(`   Reçu:`, data);
      }
      
    } catch (error) {
      console.log(`❌ ${test.name} - Erreur: ${error.message}`);
      
      if (error.message.includes('ECONNREFUSED')) {
        console.log(`   💡 Solution: Démarrer le serveur avec "node server/test-clients.js"`);
      }
    }
  }
  
  console.log('\n📊 Résumé des tests:');
  console.log(`   ✅ Tests réussis: ${passedTests}/${totalTests}`);
  console.log(`   ❌ Tests échoués: ${totalTests - passedTests}/${totalTests}`);
  
  if (passedTests === totalTests) {
    console.log('\n🎉 Tous les tests sont passés !');
    console.log('\n🚀 Instructions pour utiliser l\'application:');
    console.log('1. Ouvrir http://localhost:3000');
    console.log('2. Se <NAME_EMAIL> / Tech123');
    console.log('3. Cliquer sur l\'icône "Clients" (👥)');
    console.log('4. La liste des clients doit s\'afficher sans erreur');
    console.log('\n📋 Fonctionnalités disponibles:');
    console.log('   - Liste des clients depuis la base "Facutration"');
    console.log('   - Recherche par nom');
    console.log('   - Localisation Google Maps');
    console.log('   - Sélection de clients');
    console.log('   - Formulaire de consommation intégré');
  } else {
    console.log('\n⚠️ Certains tests ont échoué.');
    console.log('Vérifiez que:');
    console.log('1. Le serveur backend est démarré: node server/test-clients.js');
    console.log('2. PostgreSQL est en cours d\'exécution');
    console.log('3. La base "Facutration" est accessible');
  }
}

verificationFinale();
