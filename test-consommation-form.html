<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Formulaire Consommation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
            display: none;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .data-display {
            background-color: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test du Formulaire de Consommation d'Eau</h1>
        <p>Test d'intégration avec la base de données PostgreSQL "Facutration"</p>

        <div class="form-group">
            <button onclick="testConnection()">🔗 Tester la connexion serveur</button>
            <button onclick="loadContracts()">📋 Charger les contrats</button>
            <button onclick="loadConsommations()">💧 Charger les consommations</button>
        </div>

        <hr>

        <h2>📝 Ajouter une nouvelle consommation</h2>
        <form id="consommationForm">
            <div class="form-group">
                <label for="idcont">Contrat:</label>
                <select id="idcont" required>
                    <option value="">Sélectionner un contrat</option>
                </select>
            </div>

            <div class="form-group">
                <label for="consommationpre">Consommation Précédente (m³):</label>
                <input type="number" id="consommationpre" step="0.1" placeholder="Ex: 100.0">
            </div>

            <div class="form-group">
                <label for="consommationactuelle">Consommation Actuelle (m³) *:</label>
                <input type="number" id="consommationactuelle" step="0.1" placeholder="Ex: 125.5" required>
            </div>

            <div class="form-group">
                <label for="jours">Nombre de jours:</label>
                <input type="number" id="jours" value="30" placeholder="30">
            </div>

            <div class="form-group">
                <label for="periode">Période (YYYY-MM):</label>
                <input type="month" id="periode" required>
            </div>

            <button type="submit">💾 Enregistrer la consommation</button>
        </form>

        <div id="result" class="result"></div>
        <div id="dataDisplay" class="data-display" style="display: none;"></div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:3003';

        // Initialiser la date par défaut
        document.getElementById('periode').value = new Date().toISOString().slice(0, 7);

        async function testConnection() {
            showResult('🔄 Test de connexion...', 'info');
            try {
                const response = await fetch(`${API_BASE_URL}/`);
                const data = await response.json();
                showResult('✅ Connexion réussie !', 'success');
                showData(data);
            } catch (error) {
                showResult('❌ Erreur de connexion: ' + error.message, 'error');
            }
        }

        async function loadContracts() {
            showResult('🔄 Chargement des contrats...', 'info');
            try {
                const response = await fetch(`${API_BASE_URL}/api/contracts`);
                const data = await response.json();
                
                if (data.success) {
                    const select = document.getElementById('idcont');
                    select.innerHTML = '<option value="">Sélectionner un contrat</option>';
                    
                    data.data.forEach(contract => {
                        const option = document.createElement('option');
                        option.value = contract.idcontract;
                        option.textContent = `${contract.codeqr} - ${contract.nom} ${contract.prenom} (${contract.ville})`;
                        select.appendChild(option);
                    });
                    
                    showResult(`✅ ${data.data.length} contrats chargés`, 'success');
                    showData(data.data);
                } else {
                    showResult('❌ Erreur lors du chargement des contrats', 'error');
                }
            } catch (error) {
                showResult('❌ Erreur: ' + error.message, 'error');
            }
        }

        async function loadConsommations() {
            showResult('🔄 Chargement des consommations...', 'info');
            try {
                const response = await fetch(`${API_BASE_URL}/api/consommations`);
                const data = await response.json();
                
                if (data.success) {
                    showResult(`✅ ${data.data.length} consommations chargées`, 'success');
                    showData(data.data);
                } else {
                    showResult('❌ Erreur lors du chargement des consommations', 'error');
                }
            } catch (error) {
                showResult('❌ Erreur: ' + error.message, 'error');
            }
        }

        document.getElementById('consommationForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const formData = {
                idcont: document.getElementById('idcont').value,
                consommationpre: document.getElementById('consommationpre').value || 0,
                consommationactuelle: document.getElementById('consommationactuelle').value,
                jours: document.getElementById('jours').value || 30,
                periode: document.getElementById('periode').value,
                idtech: 1 // ID technicien par défaut
            };

            showResult('🔄 Enregistrement en cours...', 'info');
            
            try {
                const response = await fetch(`${API_BASE_URL}/api/consommations`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formData)
                });

                const data = await response.json();
                
                if (data.success) {
                    showResult('✅ Consommation enregistrée avec succès !', 'success');
                    showData(data.data);
                    document.getElementById('consommationForm').reset();
                    document.getElementById('periode').value = new Date().toISOString().slice(0, 7);
                } else {
                    showResult('❌ Erreur: ' + data.error, 'error');
                }
            } catch (error) {
                showResult('❌ Erreur: ' + error.message, 'error');
            }
        });

        function showResult(message, type) {
            const result = document.getElementById('result');
            result.textContent = message;
            result.className = `result ${type}`;
            result.style.display = 'block';
        }

        function showData(data) {
            const display = document.getElementById('dataDisplay');
            display.textContent = JSON.stringify(data, null, 2);
            display.style.display = 'block';
        }

        // Charger les contrats au démarrage
        window.addEventListener('load', () => {
            loadContracts();
        });
    </script>
</body>
</html>
