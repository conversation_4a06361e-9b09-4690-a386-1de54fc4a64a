require('dotenv').config();
const express = require('express');
const cors = require('cors');
const { Pool } = require('pg');

const app = express();
const PORT = 3002;

// Middleware
app.use(cors());
app.use(express.json());

// Configuration de la base de données Facutration
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'Facutration',
  password: process.env.DB_PASSWORD || '123456',
  port: process.env.DB_PORT || 5432,
});

// Test de connexion
async function testConnection() {
  try {
    console.log('🔍 Test de connexion à Facutration...');
    const client = await pool.connect();
    console.log('✅ Connexion réussie !');
    
    const result = await client.query('SELECT current_database()');
    console.log(`📊 Base de données: ${result.rows[0].current_database}`);
    
    client.release();
    return true;
  } catch (error) {
    console.error('❌ Erreur de connexion:', error.message);
    return false;
  }
}

// Route de test
app.get('/', (req, res) => {
  res.json({
    message: 'Serveur Facutration fonctionnel',
    timestamp: new Date().toISOString(),
    database: 'Facutration',
    status: 'OK'
  });
});

// Route pour tester la base de données
app.get('/api/test-db', async (req, res) => {
  try {
    const client = await pool.connect();
    const result = await client.query('SELECT current_database(), NOW()');
    client.release();
    
    res.json({
      success: true,
      database: result.rows[0].current_database,
      timestamp: result.rows[0].now
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Route pour lister les clients
app.get('/api/clients', async (req, res) => {
  try {
    const client = await pool.connect();
    const result = await client.query('SELECT * FROM client LIMIT 10');
    client.release();
    
    res.json({
      success: true,
      clients: result.rows,
      count: result.rows.length
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Démarrage du serveur
async function startServer() {
  console.log('🚀 Démarrage du serveur test Facutration...');
  
  // Test de connexion avant démarrage
  const connected = await testConnection();
  
  if (connected) {
    app.listen(PORT, () => {
      console.log(`✅ Serveur démarré sur http://localhost:${PORT}`);
      console.log(`📊 Base de données: Facutration`);
      console.log('\n📡 Routes disponibles:');
      console.log('  - GET  / (test)');
      console.log('  - GET  /api/test-db (test DB)');
      console.log('  - GET  /api/clients (clients)');
    });
  } else {
    console.error('❌ Impossible de démarrer le serveur - problème de connexion DB');
    process.exit(1);
  }
}

startServer();
