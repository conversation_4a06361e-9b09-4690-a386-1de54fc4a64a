// Vérification complète de l'application
async function verificationComplete() {
  console.log('🔍 Vérification complète de l\'application Facturation');
  console.log('=====================================================');
  
  let totalTests = 0;
  let passedTests = 0;
  
  // Test 1: Serveur principal
  console.log('\n1️⃣ Test du serveur principal...');
  try {
    const response = await fetch('http://localhost:3003/');
    const data = await response.json();
    
    if (response.ok && data.message) {
      console.log('   ✅ Serveur principal - OK');
      passedTests++;
    } else {
      console.log('   ❌ Serveur principal - Erreur');
    }
  } catch (error) {
    console.log('   ❌ Serveur principal - Non accessible');
    console.log('   💡 Démarrer avec: node server/test-clients.js');
  }
  totalTests++;
  
  // Test 2: Authentification technicien
  console.log('\n2️⃣ Test de l\'authentification technicien...');
  try {
    const response = await fetch('http://localhost:3003/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: '<EMAIL>',
        motDepass: 'Tech123'
      })
    });
    
    const data = await response.json();
    
    if (response.ok && data.success && data.user.role === 'Tech') {
      console.log('   ✅ Authentification technicien - OK');
      console.log(`   👤 Utilisateur: ${data.user.nom} ${data.user.prenom}`);
      passedTests++;
    } else {
      console.log('   ❌ Authentification technicien - Erreur');
      console.log('   📄 Réponse:', data);
    }
  } catch (error) {
    console.log('   ❌ Authentification technicien - Erreur réseau');
  }
  totalTests++;
  
  // Test 3: API Clients
  console.log('\n3️⃣ Test de l\'API clients...');
  try {
    const response = await fetch('http://localhost:3003/api/clients');
    const data = await response.json();
    
    if (response.ok && data.success && Array.isArray(data.data)) {
      console.log('   ✅ API Clients - OK');
      console.log(`   📊 ${data.data.length} clients trouvés`);
      
      if (data.data.length > 0) {
        const client = data.data[0];
        console.log(`   👤 Exemple: ${client.nom} ${client.prenom} (${client.ville})`);
      }
      passedTests++;
    } else {
      console.log('   ❌ API Clients - Erreur');
    }
  } catch (error) {
    console.log('   ❌ API Clients - Erreur réseau');
  }
  totalTests++;
  
  // Test 4: API Contrats
  console.log('\n4️⃣ Test de l\'API contrats...');
  try {
    const response = await fetch('http://localhost:3003/api/contracts');
    const data = await response.json();
    
    if (response.ok && data.success && Array.isArray(data.data)) {
      console.log('   ✅ API Contrats - OK');
      console.log(`   📊 ${data.data.length} contrats trouvés`);
      passedTests++;
    } else {
      console.log('   ❌ API Contrats - Erreur');
    }
  } catch (error) {
    console.log('   ❌ API Contrats - Erreur réseau');
  }
  totalTests++;
  
  // Test 5: API Consommations
  console.log('\n5️⃣ Test de l\'API consommations...');
  try {
    const response = await fetch('http://localhost:3003/api/consommations');
    const data = await response.json();
    
    if (response.ok && data.success && Array.isArray(data.data)) {
      console.log('   ✅ API Consommations - OK');
      console.log(`   📊 ${data.data.length} consommations trouvées`);
      passedTests++;
    } else {
      console.log('   ❌ API Consommations - Erreur');
    }
  } catch (error) {
    console.log('   ❌ API Consommations - Erreur réseau');
  }
  totalTests++;
  
  // Résumé
  console.log('\n📊 RÉSUMÉ DES TESTS');
  console.log('==================');
  console.log(`✅ Tests réussis: ${passedTests}/${totalTests}`);
  console.log(`❌ Tests échoués: ${totalTests - passedTests}/${totalTests}`);
  
  const successRate = (passedTests / totalTests) * 100;
  console.log(`📈 Taux de réussite: ${successRate.toFixed(1)}%`);
  
  if (passedTests === totalTests) {
    console.log('\n🎉 TOUS LES TESTS SONT PASSÉS !');
    console.log('\n🚀 L\'application est prête à être utilisée :');
    console.log('');
    console.log('📋 INSTRUCTIONS D\'UTILISATION :');
    console.log('1. Ouvrir http://localhost:3000');
    console.log('2. Se connecter avec :');
    console.log('   📧 Email: <EMAIL>');
    console.log('   🔑 Mot de passe: Tech123');
    console.log('3. Cliquer sur "Se Connecter"');
    console.log('4. Accéder au dashboard technicien');
    console.log('');
    console.log('🎯 FONCTIONNALITÉS DISPONIBLES :');
    console.log('   👥 Liste des clients (depuis base "Facutration")');
    console.log('   🔍 Recherche de clients');
    console.log('   📍 Localisation Google Maps');
    console.log('   💧 Formulaire de consommation');
    console.log('   📊 Consultation des contrats');
    console.log('   📈 Historique des consommations');
    console.log('');
    console.log('✅ Les erreurs "Identifiants incorrects" et "Erreur de chargement" sont résolues !');
    
  } else {
    console.log('\n⚠️ CERTAINS TESTS ONT ÉCHOUÉ');
    console.log('\n🔧 ACTIONS REQUISES :');
    
    if (passedTests === 0) {
      console.log('1. Démarrer le serveur backend :');
      console.log('   cd server && node test-clients.js');
      console.log('2. Vérifier PostgreSQL et la base "Facutration"');
    } else {
      console.log('1. Vérifier les logs du serveur pour les erreurs');
      console.log('2. Redémarrer le serveur si nécessaire');
      console.log('3. Vérifier la connexion à la base de données');
    }
  }
  
  console.log('\n📞 En cas de problème :');
  console.log('   - Vérifier que PostgreSQL est démarré');
  console.log('   - Vérifier que la base "Facutration" existe');
  console.log('   - Redémarrer les serveurs (backend et frontend)');
}

verificationComplete();
