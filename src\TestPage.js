import React from 'react';
import { useNavigate } from 'react-router-dom';

const TestPage = () => {
  const navigate = useNavigate();

  return (
    <div style={{
      padding: '20px',
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      color: 'white',
      fontFamily: 'Arial, sans-serif'
    }}>
      <div style={{
        maxWidth: '800px',
        margin: '0 auto',
        textAlign: 'center'
      }}>
        <h1>🎉 Page de Test - Navigation Réussie !</h1>
        <p style={{ fontSize: '18px', marginBottom: '30px' }}>
          Félicitations ! La navigation depuis le sidebar fonctionne parfaitement.
        </p>
        
        <div style={{
          background: 'rgba(255,255,255,0.1)',
          padding: '20px',
          borderRadius: '10px',
          marginBottom: '30px'
        }}>
          <h2>✅ Navigation Configurée</h2>
          <p>Les liens du sidebar redirigent maintenant vers les pages correspondantes :</p>
          <ul style={{ textAlign: 'left', maxWidth: '400px', margin: '0 auto' }}>
            <li>👥 Clients → /listes-clients</li>
            <li>💧 Consommation → /consommation</li>
            <li>📄 Factures → /factures</li>
            <li>📱 Scanner QR → /scanner</li>
            <li>🗺️ Localisation → /map</li>
            <li>📋 Historique → /historique</li>
            <li>👤 Profil → /profile</li>
          </ul>
        </div>

        <button
          onClick={() => navigate('/dashboard')}
          style={{
            background: 'linear-gradient(45deg, #4f46e5, #6366f1)',
            color: 'white',
            border: 'none',
            padding: '15px 30px',
            borderRadius: '10px',
            fontSize: '16px',
            cursor: 'pointer',
            boxShadow: '0 4px 15px rgba(0,0,0,0.2)'
          }}
        >
          🔙 Retour au Dashboard
        </button>
      </div>
    </div>
  );
};

export default TestPage;
