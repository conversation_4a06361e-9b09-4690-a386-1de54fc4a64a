import React, { useState } from 'react';
import axios from 'axios';
import TechnicianDashboard from './TechnicianDashboard';
import Dashboard from './components/Dashboard';
import './LoginPage.css';

function App() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [userData, setUserData] = useState(null);

  const handleLogin = async (e) => {
    e.preventDefault();
    setError('');

    console.log('Tentative de connexion avec:', { email, password });

    try {
      const response = await axios.post('http://localhost:3003/login', {
        email,
        motDepass: password
      });

      console.log('Réponse du serveur:', response.data);

      if (response.data.success) {
        // Stocker les données utilisateur
        setUserData(response.data.user);
        setIsLoggedIn(true);
        console.log('Connexion réussie pour:', response.data.user.email);
      } else {
        setError(response.data.message || 'Échec de la connexion');
      }

    } catch (err) {
      console.error('Erreur de connexion:', err);
      if (err.response) {
        setError(err.response.data?.message || 'Identifiants incorrects');
      } else if (err.request) {
        setError('Impossible de contacter le serveur. Vérifiez que le serveur est démarré.');
      } else {
        setError('Erreur de connexion');
      }
    }
  };

  const handleLogout = () => {
    setIsLoggedIn(false);
    setUserData(null);
    setEmail('');
    setPassword('');
    console.log('Déconnexion effectuée');
  };

  if (isLoggedIn) {
    // Rediriger vers le dashboard approprié selon le rôle
    if (userData.role === 'Tech') {
      return <TechnicianDashboard user={userData} onLogout={handleLogout} />;
    } else {
      // Dashboard pour Admin ou autres rôles
      return <Dashboard user={userData} onLogout={handleLogout} />;
    }
  }

  return (
    <div className="login-container">
      <div className="login-card">
        <div className="card-header">
          <h1 className="login-title">Connexion</h1>
          <p className="login-subtitle">Système de Facturation</p>
        </div>

        {error && (
          <div className="error-message">
            {error}
          </div>
        )}

        <form onSubmit={handleLogin} className="login-form">
          <div className="input-group">
            <label className="input-label">Adresse Email</label>
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              className="login-input"
              placeholder="<EMAIL>"
            />
          </div>

          <div className="input-group">
            <label className="input-label">Mot de Passe</label>
            <input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              className="login-input"
              placeholder="••••••••"
            />
          </div>

          <button
            type="submit"
            className="submit-button"
          >
            Se Connecter
          </button>
        </form>
      </div>
    </div>
  );
}

export default App;