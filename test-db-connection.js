require('dotenv').config();
const { Pool } = require('pg');

// Configuration de la base de données
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'Facutration',
  password: process.env.DB_PASSWORD || '123456',
  port: process.env.DB_PORT || 5432,
});

async function testConnection() {
  try {
    console.log('🔍 Test de connexion à PostgreSQL...');
    console.log(`📊 Base de données: ${process.env.DB_NAME || 'Facutration'}`);
    console.log(`🏠 Hôte: ${process.env.DB_HOST || 'localhost'}:${process.env.DB_PORT || 5432}`);
    console.log(`👤 Utilisateur: ${process.env.DB_USER || 'postgres'}`);
    
    const client = await pool.connect();
    console.log('✅ Connexion PostgreSQL réussie !');
    
    // Test de requête simple
    const result = await client.query('SELECT NOW() as current_time');
    console.log('⏰ Heure serveur:', result.rows[0].current_time);
    
    // Lister les tables
    const tables = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
      ORDER BY table_name
    `);
    
    console.log('📋 Tables disponibles:');
    tables.rows.forEach(row => {
      console.log(`  - ${row.table_name}`);
    });
    
    client.release();
    console.log('✅ Test de connexion terminé avec succès');
    
  } catch (error) {
    console.error('❌ Erreur de connexion PostgreSQL:', error.message);
    console.error('🔧 Vérifiez:');
    console.error('  - PostgreSQL est démarré');
    console.error('  - Les paramètres de connexion dans .env');
    console.error('  - Le nom de la base de données existe');
    console.error('  - L\'utilisateur a les permissions');
  } finally {
    await pool.end();
  }
}

testConnection();
