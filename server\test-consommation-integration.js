require('dotenv').config();
const { Pool } = require('pg');

// Configuration de la base de données
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'Facutration',
  password: process.env.DB_PASSWORD || '123456',
  port: process.env.DB_PORT || 5432,
});

async function testDatabaseIntegration() {
  console.log('🔍 Test d\'intégration de la base de données Facutration');
  console.log('================================================');

  try {
    const client = await pool.connect();

    // Test 1: Vérifier la connexion
    console.log('\n1. Test de connexion à la base de données...');
    const connectionTest = await client.query('SELECT NOW()');
    console.log('✅ Connexion réussie:', connectionTest.rows[0].now);

    // Test 2: Vérifier les tables existantes
    console.log('\n2. Vérification des tables...');
    const tablesQuery = `
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `;
    const tables = await client.query(tablesQuery);
    console.log('📋 Tables trouvées:');
    tables.rows.forEach(row => console.log(`   - ${row.table_name}`));

    // Test 3: Vérifier les données clients
    console.log('\n3. Test des données clients...');
    const clientsQuery = 'SELECT COUNT(*) as count FROM client';
    const clientsResult = await client.query(clientsQuery);
    console.log(`📊 Nombre de clients: ${clientsResult.rows[0].count}`);

    if (clientsResult.rows[0].count > 0) {
      const sampleClients = await client.query('SELECT * FROM client LIMIT 3');
      console.log('👥 Exemples de clients:');
      sampleClients.rows.forEach(client => {
        console.log(`   - ${client.nom} ${client.prenom} (${client.ville})`);
      });
    }

    // Test 4: Vérifier les contrats
    console.log('\n4. Test des contrats...');
    const contractsQuery = 'SELECT COUNT(*) as count FROM contract';
    const contractsResult = await client.query(contractsQuery);
    console.log(`📊 Nombre de contrats: ${contractsResult.rows[0].count}`);

    // Test 5: Ajouter des données de test si nécessaire
    if (contractsResult.rows[0].count === 0) {
      console.log('\n5. Ajout de données de test...');
      
      // Ajouter des contrats de test
      const insertContractQuery = `
        INSERT INTO contract (codeqr, datecontract, idclient, marquecompteur, numseriecompteur, posx, posy)
        VALUES 
        ('QR001', NOW(), 1, 'Sensus', 'SN123456', '33.5731', '-7.5898'),
        ('QR002', NOW(), 2, 'Itron', 'SN789012', '34.0209', '-6.8416'),
        ('QR003', NOW(), 3, 'Kamstrup', 'SN345678', '34.2619', '-6.5981')
        ON CONFLICT DO NOTHING
        RETURNING *
      `;
      
      try {
        const contractResult = await client.query(insertContractQuery);
        console.log(`✅ ${contractResult.rows.length} contrats ajoutés`);
      } catch (err) {
        console.log('⚠️ Erreur lors de l\'ajout des contrats (peut-être déjà existants):', err.message);
      }
    }

    // Test 6: Vérifier les consommations
    console.log('\n6. Test des consommations...');
    const consommationsQuery = 'SELECT COUNT(*) as count FROM consommation';
    const consommationsResult = await client.query(consommationsQuery);
    console.log(`📊 Nombre de consommations: ${consommationsResult.rows[0].count}`);

    if (consommationsResult.rows[0].count > 0) {
      const sampleConsommations = await client.query(`
        SELECT 
          cons.idcons,
          cons.consommationactuelle,
          cons.periode,
          co.codeqr,
          c.nom,
          c.prenom
        FROM consommation cons
        LEFT JOIN contract co ON cons.idcont = co.idcontract
        LEFT JOIN client c ON co.idclient = c.idclient
        LIMIT 3
      `);
      console.log('💧 Exemples de consommations:');
      sampleConsommations.rows.forEach(cons => {
        console.log(`   - ${cons.nom} ${cons.prenom}: ${cons.consommationactuelle}m³ (${cons.periode})`);
      });
    }

    // Test 7: Test de l'API de récupération des contrats
    console.log('\n7. Test de la requête contrats avec clients...');
    const contractsWithClientsQuery = `
      SELECT 
        co.idcontract,
        co.codeqr,
        co.marquecompteur,
        c.nom,
        c.prenom,
        c.ville
      FROM contract co
      LEFT JOIN client c ON co.idclient = c.idclient
      LIMIT 5
    `;
    const contractsWithClients = await client.query(contractsWithClientsQuery);
    console.log(`📋 Contrats avec clients (${contractsWithClients.rows.length}):`);
    contractsWithClients.rows.forEach(contract => {
      console.log(`   - ${contract.codeqr}: ${contract.nom} ${contract.prenom} (${contract.ville})`);
    });

    client.release();
    console.log('\n✅ Tous les tests sont passés avec succès !');
    console.log('\n🚀 Vous pouvez maintenant:');
    console.log('   1. Démarrer le serveur: node server/test-clients.js');
    console.log('   2. Accéder au formulaire de consommation dans l\'application React');
    console.log('   3. Tester l\'ajout de nouvelles consommations');

  } catch (error) {
    console.error('❌ Erreur lors du test:', error);
  } finally {
    await pool.end();
  }
}

// Exécuter le test
testDatabaseIntegration();
