import React, { useState, useEffect } from 'react';
import '../TechnicianDashboard.css';

const MapPage = ({ onBack }) => {
  const [clients, setClients] = useState([]);
  const [selectedClient, setSelectedClient] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchClients();
  }, []);

  const fetchClients = async () => {
    try {
      setLoading(true);
      const response = await fetch('http://localhost:3003/api/clients');
      const data = await response.json();
      setClients(data.clients || []);
    } catch (error) {
      console.error('Erreur lors du chargement des clients:', error);
    } finally {
      setLoading(false);
    }
  };

  const openGoogleMaps = (client) => {
    if (client.latitude && client.longitude) {
      const url = `https://www.google.com/maps?q=${client.latitude},${client.longitude}&z=15`;
      window.open(url, '_blank');
    } else {
      alert('Coordonnées GPS non disponibles pour ce client');
    }
  };

  const openDirections = (client) => {
    if (client.latitude && client.longitude) {
      const url = `https://www.google.com/maps/dir/?api=1&destination=${client.latitude},${client.longitude}`;
      window.open(url, '_blank');
    } else {
      alert('Coordonnées GPS non disponibles pour ce client');
    }
  };

  if (loading) {
    return (
      <div className="tech-mobile-content">
        <div className="tech-mobile-card">
          <div className="tech-mobile-card-header">
            <button onClick={onBack} className="tech-mobile-back-btn">
              ← Retour
            </button>
            <h1 className="tech-mobile-card-title">Localisation Clients</h1>
          </div>
          <div className="tech-mobile-loading">
            <div className="tech-mobile-spinner"></div>
            <p>Chargement de la carte...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="tech-mobile-content">
      <div className="tech-mobile-card">
        <div className="tech-mobile-card-header">
          <button onClick={onBack} className="tech-mobile-back-btn">
            ← Retour
          </button>
          <div>
            <h1 className="tech-mobile-card-title">Localisation Clients</h1>
            <p className="tech-mobile-card-subtitle">
              {clients.length} client(s) géolocalisé(s)
            </p>
          </div>
        </div>
      </div>

      {/* Actions rapides */}
      <div className="tech-mobile-card">
        <div className="tech-mobile-card-header">
          <h2 className="tech-mobile-card-title">Actions Rapides</h2>
        </div>
        <div className="tech-mobile-intervention-actions" style={{ padding: '20px' }}>
          <button
            className="tech-mobile-action-btn complete"
            onClick={() => {
              const allCoords = clients
                .filter(c => c.latitude && c.longitude)
                .map(c => `${c.latitude},${c.longitude}`)
                .join('|');
              if (allCoords) {
                const url = `https://www.google.com/maps?q=${allCoords}`;
                window.open(url, '_blank');
              }
            }}
          >
            🗺️ Voir tous les clients
          </button>
          <button
            className="tech-mobile-action-btn start"
            onClick={() => {
              if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition((position) => {
                  const { latitude, longitude } = position.coords;
                  const url = `https://www.google.com/maps?q=${latitude},${longitude}&z=15`;
                  window.open(url, '_blank');
                });
              } else {
                alert('Géolocalisation non supportée');
              }
            }}
          >
            📍 Ma position
          </button>
        </div>
      </div>

      {/* Liste des clients avec localisation */}
      {clients.length === 0 ? (
        <div className="tech-mobile-card">
          <div className="tech-mobile-empty-state">
            <div className="tech-mobile-empty-icon">🗺️</div>
            <h3>Aucun client trouvé</h3>
            <p>Aucun client n'est disponible pour la localisation.</p>
          </div>
        </div>
      ) : (
        clients.map(client => (
          <div key={client.id} className="tech-mobile-intervention-item">
            <div className="tech-mobile-intervention-header">
              <div className="tech-mobile-intervention-client">
                <strong>{client.nom}</strong>
                <div style={{ fontSize: '14px', color: '#6b7280', marginTop: '2px' }}>
                  {client.adresse}
                </div>
              </div>
              <div className="tech-mobile-intervention-badge">
                {client.latitude && client.longitude ? '📍 GPS' : '❌ Pas de GPS'}
              </div>
            </div>

            <div className="tech-mobile-intervention-details">
              <div className="tech-mobile-intervention-info">
                <span>📞 {client.telephone}</span>
              </div>
              <div className="tech-mobile-intervention-info">
                <span>✉️ {client.email}</span>
              </div>
              {client.latitude && client.longitude && (
                <div className="tech-mobile-intervention-info">
                  <span>🌍 {client.latitude}, {client.longitude}</span>
                </div>
              )}
            </div>

            {client.latitude && client.longitude && (
              <div className="tech-mobile-intervention-actions">
                <button
                  className="tech-mobile-action-btn start"
                  onClick={() => openGoogleMaps(client)}
                >
                  Voir sur la carte
                </button>
                <button
                  className="tech-mobile-action-btn complete"
                  onClick={() => openDirections(client)}
                >
                  Itinéraire
                </button>
              </div>
            )}
          </div>
        ))
      )}

      {/* Informations sur la géolocalisation */}
      <div className="tech-mobile-card">
        <div className="tech-mobile-card-header">
          <h2 className="tech-mobile-card-title">ℹ️ Informations</h2>
        </div>
        <div style={{ padding: '20px' }}>
          <p style={{ color: '#6b7280', fontSize: '14px', lineHeight: '1.5', margin: 0 }}>
            • Cliquez sur "Voir sur la carte" pour ouvrir Google Maps avec la position du client<br/>
            • Cliquez sur "Itinéraire" pour obtenir les directions depuis votre position<br/>
            • Utilisez "Ma position" pour voir votre localisation actuelle<br/>
            • "Voir tous les clients" affiche tous les clients géolocalisés sur une seule carte
          </p>
        </div>
      </div>
    </div>
  );
};

export default MapPage;
