# 🧪 Guide d'utilisation du Formulaire de Consommation d'Eau

## 📋 Vue d'ensemble

Le formulaire de consommation d'eau a été intégré avec succès à votre base de données PostgreSQL "Facutration". Il utilise les tables `consommation` et `Contract` pour gérer les relevés de consommation d'eau.

## 🚀 Démarrage

### 1. <PERSON><PERSON><PERSON><PERSON> le serveur backend

```bash
cd server
node consommation-server.js
```

Le serveur démarre sur `http://localhost:3003` avec les routes suivantes :
- `GET /` - Test de connexion
- `GET /api/contracts` - Liste des contrats avec informations clients
- `GET /api/consommations` - Liste des consommations
- `POST /api/consommations` - Ajouter une nouvelle consommation

### 2. Tester l'intégration

Ouvrez le fichier `test-consommation-form.html` dans votre navigateur pour tester l'intégration complète.

## 📊 Structure des données

### Table Contract
- `idcontract` - ID unique du contrat
- `codeqr` - Code QR du compteur
- `datecontract` - Date du contrat
- `idclient` - Référence vers le client
- `marquecompteur` - Marque du compteur
- `numseriecompteur` - Numéro de série
- `posx`, `posy` - Coordonnées GPS

### Table Consommation
- `idcons` - ID unique de la consommation
- `consommationpre` - Consommation précédente
- `consommationactuelle` - Consommation actuelle
- `idcont` - Référence vers le contrat
- `idtech` - Référence vers le technicien
- `idtranch` - Référence vers la tranche tarifaire
- `jours` - Nombre de jours de la période
- `periode` - Période (YYYY-MM)
- `status` - Statut du relevé

## 🔧 Utilisation dans l'application React

### 1. Accéder au formulaire

1. Connectez-vous avec `<EMAIL>` / `Tech123`
2. Cliquez sur l'icône "Consommation" (💧) dans la navigation
3. Le formulaire se charge avec les contrats disponibles

### 2. Saisir une consommation

1. **Sélectionner un contrat** : Choisissez dans la liste déroulante
2. **Consommation précédente** : Optionnel, valeur du relevé précédent
3. **Consommation actuelle** : Obligatoire, valeur du nouveau relevé
4. **Nombre de jours** : Par défaut 30 jours
5. **Période** : Format YYYY-MM, par défaut le mois actuel

### 3. Visualiser les relevés

Les consommations récentes s'affichent automatiquement avec :
- Nom du client
- Valeur de consommation
- Période
- Code QR du compteur
- Nom du technicien
- Adresse du client

## 🔍 API Endpoints

### GET /api/contracts
Récupère tous les contrats avec les informations clients associées.

**Réponse :**
```json
{
  "success": true,
  "message": "Contrats récupérés avec succès",
  "count": 2,
  "data": [
    {
      "idcontract": 1,
      "codeqr": "QR001234567890",
      "marquecompteur": "SENSUS",
      "nom": "loukil",
      "prenom": "bahija",
      "ville": "Sefrou"
    }
  ]
}
```

### GET /api/consommations
Récupère toutes les consommations avec les détails des contrats et clients.

### POST /api/consommations
Ajoute une nouvelle consommation.

**Corps de la requête :**
```json
{
  "idcont": 1,
  "consommationpre": 100.0,
  "consommationactuelle": 125.5,
  "jours": 30,
  "periode": "2025-06",
  "idtech": 1
}
```

## 🛠️ Fichiers modifiés

1. **`server/test-clients.js`** - Serveur principal avec toutes les routes API
2. **`server/consommation-server.js`** - Serveur dédié pour les tests
3. **`src/pages/ConsommationPage.js`** - Formulaire React mis à jour
4. **`test-consommation-form.html`** - Page de test HTML

## 🔧 Dépannage

### Problème de connexion à la base de données
```bash
node server/simple-db-test.js
```

### Vérifier les données de test
```bash
node server/add-test-contracts.js
```

### Tester les routes API
Utilisez le fichier `test-consommation-form.html` pour tester toutes les fonctionnalités.

## ✅ Fonctionnalités implémentées

- ✅ Connexion à la base PostgreSQL "Facutration"
- ✅ Récupération des contrats avec informations clients
- ✅ Affichage dynamique dans le formulaire
- ✅ Sauvegarde des consommations dans la base
- ✅ Affichage des relevés récents
- ✅ Validation des données
- ✅ Gestion des erreurs
- ✅ Interface mobile-friendly

## 🚀 Prochaines étapes

1. Intégrer le scanner QR pour sélectionner automatiquement un contrat
2. Ajouter la géolocalisation pour vérifier la position du technicien
3. Implémenter la synchronisation hors ligne
4. Ajouter des notifications push pour les relevés en retard

Le formulaire est maintenant entièrement fonctionnel et connecté à votre base de données PostgreSQL "Facutration" !
