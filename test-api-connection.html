<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test API Connection</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background: #45a049;
        }
        .result {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success { border-left: 4px solid #4CAF50; }
        .error { border-left: 4px solid #f44336; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Test de Connexion API Backend</h1>
        <p>Ce test vérifie la connexion entre le frontend et le backend sur le port 3002</p>
        
        <div>
            <button onclick="testConnection()">🔗 Tester la Connexion</button>
            <button onclick="testClients()">👥 Tester API Clients</button>
            <button onclick="testFactures()">📄 Tester API Factures</button>
            <button onclick="testSchema()">📋 Tester Schéma DB</button>
            <button onclick="clearResults()">🗑️ Effacer</button>
        </div>
        
        <div id="results"></div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:3002';
        
        function addResult(message, isSuccess = true) {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `result ${isSuccess ? 'success' : 'error'}`;
            div.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        async function testConnection() {
            addResult('🔄 Test de connexion au serveur backend...');
            
            try {
                const response = await fetch(`${API_BASE_URL}/`);
                if (response.ok) {
                    const data = await response.json();
                    addResult(`✅ Serveur accessible: ${JSON.stringify(data)}`, true);
                } else {
                    addResult(`❌ Erreur HTTP: ${response.status}`, false);
                }
            } catch (error) {
                addResult(`❌ Erreur de connexion: ${error.message}`, false);
                addResult(`💡 Vérifiez que le serveur Node.js est démarré sur le port 3002`, false);
            }
        }
        
        async function testClients() {
            addResult('🔄 Test de l\'API clients...');
            
            try {
                const response = await fetch(`${API_BASE_URL}/api/table/client`);
                if (response.ok) {
                    const data = await response.json();
                    addResult(`✅ API Clients: ${data.data?.length || 0} clients trouvés`, true);
                    addResult(`📊 Données: ${JSON.stringify(data, null, 2)}`, true);
                } else {
                    addResult(`❌ Erreur API Clients: ${response.status}`, false);
                }
            } catch (error) {
                addResult(`❌ Erreur API Clients: ${error.message}`, false);
            }
        }
        
        async function testFactures() {
            addResult('🔄 Test de l\'API factures...');
            
            try {
                const response = await fetch(`${API_BASE_URL}/api/table/facture`);
                if (response.ok) {
                    const data = await response.json();
                    addResult(`✅ API Factures: ${data.data?.length || 0} factures trouvées`, true);
                } else if (response.status === 404) {
                    const errorData = await response.json();
                    addResult(`⚠️ Table facture non trouvée: ${errorData.message}`, false);
                    addResult(`📋 Tables disponibles: ${errorData.available_tables?.join(', ')}`, true);
                } else {
                    addResult(`❌ Erreur API Factures: ${response.status}`, false);
                }
            } catch (error) {
                addResult(`❌ Erreur API Factures: ${error.message}`, false);
            }
        }
        
        async function testSchema() {
            addResult('🔄 Test du schéma de base de données...');
            
            try {
                const response = await fetch(`${API_BASE_URL}/api/database/schema`);
                if (response.ok) {
                    const data = await response.json();
                    addResult(`✅ Schéma DB: ${data.tables?.length || 0} tables détectées`, true);
                    addResult(`📋 Tables: ${data.tables?.map(t => t.table_name).join(', ')}`, true);
                } else {
                    addResult(`❌ Erreur Schéma: ${response.status}`, false);
                }
            } catch (error) {
                addResult(`❌ Erreur Schéma: ${error.message}`, false);
            }
        }
        
        // Test automatique au chargement
        window.onload = function() {
            addResult('🚀 Page de test chargée');
            addResult('💡 Cliquez sur les boutons pour tester les différentes APIs');
        };
    </script>
</body>
</html>
