import React, { useState, useEffect } from 'react';
import '../TechnicianDashboard.css';

const ResultatsReleveePage = ({ onBack, newReleve }) => {
  const [releves, setReleves] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Simuler le chargement des relevés récents
    const loadReleves = () => {
      setLoading(true);
      
      // Données simulées des relevés récents
      const relevesData = [
        {
          id: 1,
          client: 'client1 client1',
          periode: '2025-06',
          compteur: 'QR123',
          precedente: '100 m³',
          actuelle: '125 m³',
          consommation: '25 m³',
          technicien: 'Administrateur Système',
          adresse: '123 Rue Allal Ben Abdellah'
        },
        {
          id: 2,
          client: 'client1 client1',
          periode: '2025-06',
          compteur: 'QR123',
          precedente: '150 m³',
          actuelle: '175 m³',
          consommation: '25 m³',
          technicien: 'Administrateur Système',
          adresse: '123 Rue Allal <PERSON>'
        },
        {
          id: 3,
          client: 'client1 client1',
          periode: '2025-06',
          compteur: 'QR123',
          precedente: '100 m³',
          actuelle: '300 m³',
          consommation: '200 m³',
          technicien: 'Administrateur Système',
          adresse: '123 Rue Allal Ben Abdellah'
        }
      ];

      // Si un nouveau relevé a été ajouté, l'inclure en premier
      if (newReleve) {
        const nouveauReleve = {
          id: Date.now(),
          client: newReleve.clientNom || 'Client',
          periode: newReleve.periode,
          compteur: newReleve.compteur || 'QR123',
          precedente: newReleve.consommationpre + ' m³',
          actuelle: newReleve.consommationactuelle + ' m³',
          consommation: (newReleve.consommationactuelle - newReleve.consommationpre) + ' m³',
          technicien: 'Administrateur Système',
          adresse: newReleve.adresse || '123 Rue Allal Ben Abdellah'
        };
        relevesData.unshift(nouveauReleve);
      }

      setReleves(relevesData);
      setLoading(false);
    };

    loadReleves();
  }, [newReleve]);

  if (loading) {
    return (
      <div className="tech-mobile-content">
        <div className="tech-mobile-card">
          <div className="tech-mobile-card-header">
            <button onClick={onBack} className="tech-mobile-back-btn">
              ← Retour
            </button>
            <h1 className="tech-mobile-card-title">Résultats des Relevés</h1>
          </div>
          <div className="tech-mobile-loading">
            <div className="tech-mobile-spinner"></div>
            <p>Chargement des résultats...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="tech-mobile-content">
      {/* En-tête */}
      <div className="tech-mobile-card">
        <div className="tech-mobile-card-header">
          <button onClick={onBack} className="tech-mobile-back-btn">
            ← Retour
          </button>
          <div>
            <h1 className="tech-mobile-card-title">Résultats des Relevés</h1>
            <p className="tech-mobile-card-subtitle">Relevés enregistrés avec succès</p>
          </div>
        </div>
      </div>

      {/* Message de succès si nouveau relevé */}
      {newReleve && (
        <div className="tech-mobile-card" style={{ 
          background: 'linear-gradient(45deg, #10b981, #059669)', 
          color: 'white',
          marginBottom: '20px'
        }}>
          <div style={{ textAlign: 'center', padding: '10px' }}>
            <h3 style={{ margin: '0 0 5px 0', fontSize: '18px' }}>✅ Relevé Enregistré</h3>
            <p style={{ margin: '0', fontSize: '14px', opacity: '0.9' }}>
              Le relevé a été enregistré avec succès dans la base de données
            </p>
          </div>
        </div>
      )}

      {/* Liste des relevés */}
      {releves.map((releve, index) => (
        <div key={releve.id} className="tech-mobile-intervention-item">
          <div className="tech-mobile-intervention-header">
            <div className="tech-mobile-intervention-client">
              <strong>{releve.client}</strong>
            </div>
            <div className="tech-mobile-intervention-badge" style={{
              background: releve.consommation.includes('200') ? '#ef4444' : 
                         releve.consommation.includes('25') ? '#3b82f6' : '#10b981',
              color: 'white',
              padding: '4px 8px',
              borderRadius: '6px',
              fontSize: '12px',
              fontWeight: '600'
            }}>
              {releve.consommation}
            </div>
          </div>

          <div className="tech-mobile-intervention-details">
            <div>
              <span style={{ fontSize: '12px', color: '#6b7280' }}>📅 Période:</span>
              <div style={{ fontWeight: '600', color: '#1f2937' }}>{releve.periode}</div>
            </div>
            <div>
              <span style={{ fontSize: '12px', color: '#6b7280' }}>🔢 Compteur:</span>
              <div style={{ fontWeight: '600', color: '#1f2937' }}>{releve.compteur}</div>
            </div>
            <div>
              <span style={{ fontSize: '12px', color: '#6b7280' }}>👤 Technicien:</span>
              <div style={{ fontWeight: '600', color: '#1f2937' }}>{releve.technicien}</div>
            </div>
            <div>
              <span style={{ fontSize: '12px', color: '#6b7280' }}>📊 Précédente:</span>
              <div style={{ fontWeight: '600', color: '#1f2937' }}>{releve.precedente}</div>
            </div>
          </div>

          <div style={{ marginTop: '10px', padding: '8px', background: '#f8fafc', borderRadius: '8px' }}>
            <div style={{ fontSize: '12px', color: '#6b7280', marginBottom: '4px' }}>📍 Adresse:</div>
            <div style={{ fontSize: '14px', color: '#1f2937' }}>{releve.adresse}</div>
          </div>
        </div>
      ))}

      {releves.length === 0 && (
        <div className="tech-mobile-card" style={{ textAlign: 'center', padding: '40px 20px' }}>
          <div style={{ fontSize: '48px', marginBottom: '20px' }}>📊</div>
          <h3 style={{ color: '#6b7280', marginBottom: '10px' }}>Aucun relevé trouvé</h3>
          <p style={{ color: '#9ca3af' }}>Les relevés enregistrés apparaîtront ici</p>
        </div>
      )}
    </div>
  );
};

export default ResultatsReleveePage;
