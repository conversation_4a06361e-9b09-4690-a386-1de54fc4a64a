import React, { useState, useEffect } from 'react';
import '../TechnicianDashboard.css';

const ResultatsReleveePage = ({ onBack, newReleve }) => {
  const [releves, setReleves] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const API_BASE_URL = 'http://localhost:3000';

  // Charger tous les relevés depuis la base de données
  const fetchAllReleves = async () => {
    try {
      setLoading(true);
      const response = await fetch(`${API_BASE_URL}/api/consommations`);
      const data = await response.json();

      if (data.success && data.data) {
        // Transformer les données pour correspondre au format d'affichage
        const formattedReleves = data.data.map(consommation => ({
          id: consommation.idcons,
          client: `${consommation.nom || 'Client'} ${consommation.prenom || ''}`.trim(),
          periode: consommation.periode || '2025-06',
          compteur: consommation.codeqr || 'QR123',
          precedente: `${consommation.consommationpre || 0} m³`,
          actuelle: `${consommation.consommationactuelle || 0} m³`,
          consommation: `${(parseFloat(consommation.consommationactuelle || 0) - parseFloat(consommation.consommationpre || 0))} m³`,
          technicien: 'Administrateur Système',
          adresse: consommation.adresse || '123 Rue Allal Ben Abdellah'
        }));

        setReleves(formattedReleves);
        console.log('✅ Relevés chargés:', formattedReleves.length);
      } else {
        console.error('❌ Erreur lors du chargement des relevés:', data.message);
        setError('Erreur lors du chargement des relevés');
      }
    } catch (error) {
      console.error('❌ Erreur de connexion:', error);
      setError('Erreur de connexion au serveur');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAllReleves();
  }, []);

  if (loading) {
    return (
      <div className="tech-mobile-content">
        <div className="tech-mobile-card">
          <div className="tech-mobile-card-header">
            <button onClick={onBack} className="tech-mobile-back-btn">
              ← Retour
            </button>
            <h1 className="tech-mobile-card-title">Résultats des Relevés</h1>
          </div>
          <div className="tech-mobile-loading">
            <div className="tech-mobile-spinner"></div>
            <p>Chargement des résultats...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="tech-mobile-content">
        <div className="tech-mobile-card">
          <div className="tech-mobile-card-header">
            <button onClick={onBack} className="tech-mobile-back-btn">
              ← Retour
            </button>
            <h1 className="tech-mobile-card-title">Erreur</h1>
          </div>
          <div style={{ textAlign: 'center', padding: '20px' }}>
            <p style={{ color: '#ef4444', marginBottom: '20px' }}>{error}</p>
            <button
              onClick={fetchAllReleves}
              className="tech-mobile-action-btn"
              style={{ margin: '10px auto' }}
            >
              Réessayer
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="tech-mobile-content">
      {/* En-tête */}
      <div className="tech-mobile-card">
        <div className="tech-mobile-card-header">
          <button onClick={onBack} className="tech-mobile-back-btn">
            ← Retour
          </button>
          <div>
            <h1 className="tech-mobile-card-title">Relevés Récents</h1>
            <p className="tech-mobile-card-subtitle">{releves.length} relevé(s)</p>
          </div>
        </div>
      </div>

      {/* Message de succès si nouveau relevé */}
      {newReleve && (
        <div className="tech-mobile-card" style={{ 
          background: 'linear-gradient(45deg, #10b981, #059669)', 
          color: 'white',
          marginBottom: '20px'
        }}>
          <div style={{ textAlign: 'center', padding: '10px' }}>
            <h3 style={{ margin: '0 0 5px 0', fontSize: '18px' }}>✅ Relevé Enregistré</h3>
            <p style={{ margin: '0', fontSize: '14px', opacity: '0.9' }}>
              Le relevé a été enregistré avec succès dans la base de données
            </p>
          </div>
        </div>
      )}

      {/* Liste des relevés - Format exact de l'image */}
      {releves.map((releve, index) => (
        <div key={releve.id} className="releve-result-card">
          <div className="releve-result-header">
            <div className="releve-client-name">
              <strong>{releve.client}</strong>
            </div>
            <div className="releve-consommation-badge">
              {releve.consommation}
            </div>
          </div>

          <div className="releve-result-content">
            <div className="releve-info-row">
              <div className="releve-info-item">
                <span className="releve-icon">📅</span>
                <span className="releve-label">Période:</span>
                <span className="releve-value">{releve.periode}</span>
              </div>
              <div className="releve-info-item">
                <span className="releve-icon">🔢</span>
                <span className="releve-label">Compteur:</span>
                <span className="releve-value">{releve.compteur}</span>
              </div>
            </div>

            <div className="releve-info-row">
              <div className="releve-info-item">
                <span className="releve-icon">👤</span>
                <span className="releve-label">Technicien:</span>
                <span className="releve-value">{releve.technicien}</span>
              </div>
              <div className="releve-info-item">
                <span className="releve-icon">📊</span>
                <span className="releve-label">Précédente:</span>
                <span className="releve-value">{releve.precedente}</span>
              </div>
            </div>

            <div className="releve-address">
              <span className="releve-icon">📍</span>
              <span className="releve-address-text">{releve.adresse}</span>
            </div>
          </div>
        </div>
      ))}

      {releves.length === 0 && (
        <div className="tech-mobile-card" style={{ textAlign: 'center', padding: '40px 20px' }}>
          <div style={{ fontSize: '48px', marginBottom: '20px' }}>📊</div>
          <h3 style={{ color: '#6b7280', marginBottom: '10px' }}>Aucun relevé trouvé</h3>
          <p style={{ color: '#9ca3af' }}>Les relevés enregistrés apparaîtront ici</p>
        </div>
      )}

      {/* Navigation en bas comme dans l'image */}
      <div className="releve-bottom-navigation">
        <div className="releve-nav-item">
          <div className="releve-nav-icon">📊</div>
          <div className="releve-nav-label">VUE D'ENSEMBLE</div>
        </div>
        <div className="releve-nav-item">
          <div className="releve-nav-icon">👥</div>
          <div className="releve-nav-label">CLIENTS</div>
        </div>
        <div className="releve-nav-item active">
          <div className="releve-nav-icon">💧</div>
          <div className="releve-nav-label">CONSOMMATION</div>
        </div>
        <div className="releve-nav-item">
          <div className="releve-nav-icon">📄</div>
          <div className="releve-nav-label">FACTURES</div>
        </div>
        <div className="releve-nav-item">
          <div className="releve-nav-icon">📱</div>
          <div className="releve-nav-label">SCANNER QR</div>
        </div>
        <div className="releve-nav-item">
          <div className="releve-nav-icon">🗺️</div>
          <div className="releve-nav-label">LOCALISATION</div>
        </div>
      </div>
    </div>
  );
};

export default ResultatsReleveePage;
