# Serveur TechnicianDashboard - Documentation

## Description
Ce serveur Node.js est spécialement conçu pour le TechnicianDashboard et se connecte à votre base de données PostgreSQL "Facturation". Il fournit toutes les API nécessaires pour consulter et manipuler les données des tables de votre système de facturation.

## Configuration

### Prérequis
- Node.js installé
- PostgreSQL avec la base de données "Facturation"
- Les packages npm : `express`, `cors`, `pg`, `dotenv`

### Variables d'environnement
Créez un fichier `.env` dans le dossier `server/` avec :
```
DB_USER=postgres
DB_HOST=localhost
DB_NAME=Facturation
DB_PASSWORD=123456
DB_PORT=5432
PORT=3002
```

## Démarrage

### 1. Tester la connexion à la base de données
```bash
node server/test-technician-server.js
```

### 2. Démarrer le serveur
```bash
node server/technician-server.js
```

Le serveur démarre sur `http://localhost:3002`

## Routes API Disponibles

### Routes GET (Consultation)

#### 1. **GET /api/clients**
Récupère tous les clients avec leurs secteurs
```json
{
  "success": true,
  "data": [
    {
      "idclient": 1,
      "nom": "Dupont",
      "prenom": "Jean",
      "adresse": "123 Rue de la Paix",
      "ville": "Paris",
      "tel": "0123456789",
      "email": "<EMAIL>",
      "secteur_nom": "Centre-ville"
    }
  ],
  "count": 1
}
```

#### 2. **GET /api/consommations**
Récupère toutes les consommations avec détails clients et techniciens
```json
{
  "success": true,
  "data": [
    {
      "idcons": 1,
      "consommationpre": 1000,
      "consommationactuelle": 1250,
      "periode": "2024-01",
      "status": "active",
      "client_nom": "Dupont",
      "client_prenom": "Jean",
      "codeqr": "QR123456",
      "technicien_nom": "Martin",
      "technicien_prenom": "Pierre"
    }
  ],
  "count": 1
}
```

#### 3. **GET /api/factures**
Récupère toutes les factures avec détails clients
```json
{
  "success": true,
  "data": [
    {
      "idfact": 1,
      "date": "2024-01-15T10:00:00.000Z",
      "montant": 125.50,
      "periode": "2024-01",
      "status": "payée",
      "client_nom": "Dupont",
      "client_prenom": "Jean"
    }
  ],
  "count": 1
}
```

#### 4. **GET /api/contracts**
Récupère tous les contrats avec positions GPS
```json
{
  "success": true,
  "data": [
    {
      "idcontract": 1,
      "codeqr": "QR123456",
      "marquecompteur": "Sensus",
      "numseriecompteur": "SN123456",
      "posx": "48.8566",
      "posy": "2.3522",
      "client_nom": "Dupont",
      "client_prenom": "Jean"
    }
  ],
  "count": 1
}
```

#### 5. **GET /api/secteurs**
Récupère tous les secteurs avec nombre de clients
```json
{
  "success": true,
  "data": [
    {
      "ids": 1,
      "nom": "Centre-ville",
      "nombre_clients": 15
    }
  ],
  "count": 1
}
```

#### 6. **GET /api/tranches**
Récupère toutes les tranches tarifaires
```json
{
  "success": true,
  "data": [
    {
      "idtranch": 1,
      "prix": 0.50,
      "valeurmin": 0,
      "valeurmax": 100
    }
  ],
  "count": 1
}
```

#### 7. **GET /api/utilisateurs**
Récupère tous les utilisateurs (techniciens et admins)
```json
{
  "success": true,
  "data": [
    {
      "idtech": 1,
      "nom": "Martin",
      "prenom": "Pierre",
      "email": "<EMAIL>",
      "role": "Tech"
    }
  ],
  "count": 1
}
```

#### 8. **GET /api/dashboard/stats**
Récupère les statistiques générales
```json
{
  "success": true,
  "data": {
    "clients": 25,
    "consommations": 150,
    "factures": {
      "total": 120,
      "montant_total": 15750.50
    },
    "contracts": 25
  }
}
```

#### 9. **GET /api/scan/:qrCode**
Scan d'un QR code pour obtenir les infos du contrat
```
GET /api/scan/QR123456
```

#### 10. **GET /api/historique/:techId**
Historique des actions d'un technicien
```
GET /api/historique/1
```

#### 11. **GET /api/clients/map**
Clients avec leurs positions GPS pour la carte
```json
{
  "success": true,
  "data": [
    {
      "idclient": 1,
      "nom": "Dupont",
      "prenom": "Jean",
      "latitude": "48.8566",
      "longitude": "2.3522",
      "codeqr": "QR123456"
    }
  ],
  "count": 1
}
```

### Routes POST (Ajout de données)

#### 12. **POST /api/consommations**
Ajouter une nouvelle consommation
```json
{
  "consommationPre": 1000,
  "consommationActuelle": 1250,
  "idContract": 1,
  "idTech": 1,
  "idTranch": 1,
  "jours": 30,
  "periode": "2024-01",
  "status": "active"
}
```

## Structure de la Base de Données

Le serveur consulte les tables suivantes :
- **Secteur** : Secteurs géographiques
- **Client** : Informations clients
- **Utilisateur** : Techniciens et administrateurs
- **Contract** : Contrats avec QR codes et positions GPS
- **Tranch** : Tranches tarifaires
- **Consommation** : Relevés de consommation
- **Facture** : Factures générées

## Utilisation avec le Frontend

### Dans votre composant React :
```javascript
// Récupérer les clients
const fetchClients = async () => {
  try {
    const response = await fetch('http://localhost:3002/api/clients');
    const data = await response.json();
    if (data.success) {
      setClients(data.data);
    }
  } catch (error) {
    console.error('Erreur:', error);
  }
};

// Ajouter une consommation
const addConsommation = async (consommationData) => {
  try {
    const response = await fetch('http://localhost:3002/api/consommations', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(consommationData)
    });
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Erreur:', error);
  }
};
```

## Logs et Debugging

Le serveur affiche des logs détaillés :
- ✅ Connexions réussies
- ❌ Erreurs de base de données
- 📥 Requêtes reçues
- 📊 Statistiques des résultats

## Sécurité

- CORS activé pour les requêtes cross-origin
- Validation des paramètres d'entrée
- Gestion d'erreurs complète
- Logs de sécurité

## Support

Pour tester le serveur :
1. Exécutez `node server/test-technician-server.js`
2. Vérifiez que toutes les tables sont accessibles
3. Démarrez le serveur avec `node server/technician-server.js`
4. Testez les routes avec un client REST ou votre frontend React
