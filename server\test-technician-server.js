require('dotenv').config();
const { Pool } = require('pg');

// Configuration de la base de données
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'Facturation',
  password: process.env.DB_PASSWORD || '123456',
  port: process.env.DB_PORT || 5432,
});

async function testDatabaseConnection() {
  console.log('🔧 Test de connexion à la base de données Facturation...\n');
  
  try {
    // Test de connexion
    const client = await pool.connect();
    console.log('✅ Connexion à la base de données réussie');
    client.release();

    // Test des tables existantes
    console.log('\n📋 Vérification des tables existantes:');
    
    const tablesResult = await pool.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `);

    if (tablesResult.rows.length === 0) {
      console.log('❌ Aucune table trouvée dans la base de données');
      return;
    }

    console.log(`✅ ${tablesResult.rows.length} tables trouvées:`);
    tablesResult.rows.forEach(row => {
      console.log(`   - ${row.table_name}`);
    });

    // Test de chaque table avec comptage des enregistrements
    console.log('\n📊 Comptage des enregistrements par table:');
    
    const tables = ['secteur', 'client', 'utilisateur', 'contract', 'tranch', 'consommation', 'facture'];
    
    for (const table of tables) {
      try {
        const countResult = await pool.query(`SELECT COUNT(*) as count FROM ${table}`);
        const count = parseInt(countResult.rows[0].count);
        console.log(`   - ${table}: ${count} enregistrement(s)`);
      } catch (error) {
        console.log(`   - ${table}: ❌ Table non trouvée ou erreur`);
      }
    }

    // Test des données de quelques tables importantes
    console.log('\n🔍 Aperçu des données:');
    
    // Test table Client
    try {
      const clientsResult = await pool.query('SELECT * FROM client LIMIT 3');
      console.log(`\n👥 Clients (${clientsResult.rows.length} premiers):`);
      clientsResult.rows.forEach(client => {
        console.log(`   - ID: ${client.idclient}, Nom: ${client.nom} ${client.prenom}, Email: ${client.email}`);
      });
    } catch (error) {
      console.log('❌ Erreur lors de la lecture des clients:', error.message);
    }

    // Test table Utilisateur
    try {
      const usersResult = await pool.query('SELECT * FROM utilisateur LIMIT 3');
      console.log(`\n👤 Utilisateurs (${usersResult.rows.length} premiers):`);
      usersResult.rows.forEach(user => {
        console.log(`   - ID: ${user.idtech}, Nom: ${user.nom} ${user.prenom}, Role: ${user.role}, Email: ${user.email}`);
      });
    } catch (error) {
      console.log('❌ Erreur lors de la lecture des utilisateurs:', error.message);
    }

    // Test table Contract
    try {
      const contractsResult = await pool.query('SELECT * FROM contract LIMIT 3');
      console.log(`\n📄 Contrats (${contractsResult.rows.length} premiers):`);
      contractsResult.rows.forEach(contract => {
        console.log(`   - ID: ${contract.idcontract}, QR: ${contract.codeqr}, Compteur: ${contract.marquecompteur}`);
      });
    } catch (error) {
      console.log('❌ Erreur lors de la lecture des contrats:', error.message);
    }

    // Test table Consommation
    try {
      const consResult = await pool.query('SELECT * FROM consommation LIMIT 3');
      console.log(`\n💧 Consommations (${consResult.rows.length} premières):`);
      consResult.rows.forEach(cons => {
        console.log(`   - ID: ${cons.idcons}, Actuelle: ${cons.consommationactuelle}, Période: ${cons.periode}`);
      });
    } catch (error) {
      console.log('❌ Erreur lors de la lecture des consommations:', error.message);
    }

    // Test table Facture
    try {
      const facturesResult = await pool.query('SELECT * FROM facture LIMIT 3');
      console.log(`\n🧾 Factures (${facturesResult.rows.length} premières):`);
      facturesResult.rows.forEach(facture => {
        console.log(`   - ID: ${facture.idfact}, Montant: ${facture.montant}€, Status: ${facture.status}`);
      });
    } catch (error) {
      console.log('❌ Erreur lors de la lecture des factures:', error.message);
    }

    console.log('\n✅ Test de la base de données terminé avec succès!');
    console.log('\n🚀 Vous pouvez maintenant démarrer le serveur avec:');
    console.log('   node server/technician-server.js');

  } catch (error) {
    console.error('❌ Erreur lors du test de la base de données:', error);
  } finally {
    await pool.end();
  }
}

// Fonction pour tester les requêtes complexes
async function testComplexQueries() {
  console.log('\n🔍 Test des requêtes complexes...\n');
  
  try {
    // Test requête clients avec secteurs
    console.log('1. Test requête clients avec secteurs:');
    const clientsWithSectors = await pool.query(`
      SELECT 
        c.idclient,
        c.nom,
        c.prenom,
        c.adresse,
        c.ville,
        s.nom as secteur_nom
      FROM Client c
      LEFT JOIN Secteur s ON c.ids = s.ids
      LIMIT 5
    `);
    console.log(`   ✅ ${clientsWithSectors.rows.length} clients avec secteurs trouvés`);

    // Test requête consommations avec détails
    console.log('\n2. Test requête consommations avec détails:');
    const consommationsDetails = await pool.query(`
      SELECT 
        cons.idcons,
        cons.consommationactuelle,
        cons.periode,
        cl.nom as client_nom,
        cont.codeqr
      FROM Consommation cons
      LEFT JOIN Contract cont ON cons.idcont = cont.idcontract
      LEFT JOIN Client cl ON cont.idclient = cl.idclient
      LIMIT 5
    `);
    console.log(`   ✅ ${consommationsDetails.rows.length} consommations avec détails trouvées`);

    // Test requête factures avec clients
    console.log('\n3. Test requête factures avec clients:');
    const facturesWithClients = await pool.query(`
      SELECT 
        f.idfact,
        f.montant,
        f.status,
        cl.nom as client_nom
      FROM Facture f
      LEFT JOIN Consommation cons ON f.idconst = cons.idcons
      LEFT JOIN Contract cont ON cons.idcont = cont.idcontract
      LEFT JOIN Client cl ON cont.idclient = cl.idclient
      LIMIT 5
    `);
    console.log(`   ✅ ${facturesWithClients.rows.length} factures avec clients trouvées`);

    console.log('\n✅ Toutes les requêtes complexes fonctionnent correctement!');

  } catch (error) {
    console.error('❌ Erreur lors du test des requêtes complexes:', error);
  }
}

// Exécuter les tests
async function runAllTests() {
  await testDatabaseConnection();
  await testComplexQueries();
}

// Démarrer les tests
runAllTests().catch(console.error);
