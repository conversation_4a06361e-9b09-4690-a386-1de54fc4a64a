require('dotenv').config();
const { Pool } = require('pg');

const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'Facutration',
  password: process.env.DB_PASSWORD || '123456',
  port: process.env.DB_PORT || 5432,
});

async function checkTableStructure() {
  try {
    console.log('🔍 Vérification de la structure des tables...\n');
    
    const client = await pool.connect();
    
    // Lister toutes les tables
    const tablesResult = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
      ORDER BY table_name
    `);
    
    console.log('📋 Tables disponibles:', tablesResult.rows.map(r => r.table_name));
    console.log('\n');
    
    // Pour chaque table, afficher sa structure
    for (const table of tablesResult.rows) {
      const tableName = table.table_name;
      console.log(`📊 Structure de la table "${tableName}":`);
      
      const columnsResult = await client.query(`
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = $1
        ORDER BY ordinal_position
      `, [tableName]);
      
      columnsResult.rows.forEach(col => {
        console.log(`  - ${col.column_name} (${col.data_type}) ${col.is_nullable === 'NO' ? 'NOT NULL' : 'NULL'}`);
      });
      
      // Afficher quelques données d'exemple
      try {
        const dataResult = await client.query(`SELECT * FROM ${tableName} LIMIT 3`);
        if (dataResult.rows.length > 0) {
          console.log(`  📄 Données d'exemple (${dataResult.rows.length} lignes):`);
          dataResult.rows.forEach((row, index) => {
            console.log(`    ${index + 1}. ${JSON.stringify(row)}`);
          });
        } else {
          console.log(`  📄 Aucune donnée dans cette table`);
        }
      } catch (error) {
        console.log(`  ❌ Erreur lors de la lecture des données: ${error.message}`);
      }
      
      console.log('\n');
    }
    
    client.release();
    console.log('✅ Vérification terminée');
    
  } catch (error) {
    console.error('❌ Erreur:', error.message);
  } finally {
    await pool.end();
  }
}

checkTableStructure();
