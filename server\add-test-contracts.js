require('dotenv').config();
const { Pool } = require('pg');

const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'Facutration',
  password: process.env.DB_PASSWORD || '123456',
  port: process.env.DB_PORT || 5432,
});

async function addTestContracts() {
  console.log('🔧 Ajout de contrats de test...');
  
  try {
    const client = await pool.connect();
    
    // Vérifier d'abord s'il y a des clients
    const clientsResult = await client.query('SELECT COUNT(*) as count FROM client');
    console.log(`📊 Nombre de clients existants: ${clientsResult.rows[0].count}`);
    
    if (clientsResult.rows[0].count === 0) {
      console.log('⚠️ Aucun client trouvé. Ajout de clients de test...');
      
      // Ajouter des secteurs d'abord
      await client.query(`
        INSERT INTO secteur (nom) VALUES 
        ('Secteur Nord'),
        ('Secteur Sud'),
        ('Secteur Est'),
        ('Secteur Ouest')
        ON CONFLICT DO NOTHING
      `);
      
      // Ajouter des clients de test
      await client.query(`
        INSERT INTO client (nom, prenom, adresse, ville, tel, email, ids) VALUES 
        ('Benali', 'Ahmed', '12 Rue de la Paix', 'Casablanca', '0612345678', '<EMAIL>', 1),
        ('Alami', 'Fatima', '34 Avenue Mohammed V', 'Rabat', '0698765432', '<EMAIL>', 2),
        ('Tazi', 'Omar', '56 Boulevard Hassan II', 'Fès', '0677889900', '<EMAIL>', 3),
        ('Idrissi', 'Khadija', '78 Rue Al Andalous', 'Marrakech', '0655443322', '<EMAIL>', 4),
        ('Benjelloun', 'Youssef', '90 Avenue des FAR', 'Agadir', '0644556677', '<EMAIL>', 1)
        ON CONFLICT DO NOTHING
      `);
      
      console.log('✅ Clients de test ajoutés');
    }
    
    // Vérifier s'il y a des contrats
    const contractsResult = await client.query('SELECT COUNT(*) as count FROM contract');
    console.log(`📊 Nombre de contrats existants: ${contractsResult.rows[0].count}`);
    
    if (contractsResult.rows[0].count === 0) {
      console.log('📝 Ajout de contrats de test...');
      
      const insertContractQuery = `
        INSERT INTO contract (codeqr, datecontract, idclient, marquecompteur, numseriecompteur, posx, posy)
        VALUES 
        ('QR001-CASA', NOW(), 1, 'Sensus', 'SN123456', '33.5731', '-7.5898'),
        ('QR002-RABAT', NOW(), 2, 'Itron', 'SN789012', '34.0209', '-6.8416'),
        ('QR003-FES', NOW(), 3, 'Kamstrup', 'SN345678', '34.2619', '-6.5981'),
        ('QR004-MARR', NOW(), 4, 'Sensus', 'SN901234', '31.6295', '-7.9811'),
        ('QR005-AGADIR', NOW(), 5, 'Itron', 'SN567890', '30.4278', '-9.5981')
        RETURNING *
      `;
      
      const contractResult = await client.query(insertContractQuery);
      console.log(`✅ ${contractResult.rows.length} contrats ajoutés`);
      
      contractResult.rows.forEach(contract => {
        console.log(`   - ${contract.codeqr} (ID: ${contract.idcontract})`);
      });
    } else {
      console.log('✅ Des contrats existent déjà');
    }
    
    // Afficher un résumé
    const summaryQuery = `
      SELECT 
        co.idcontract,
        co.codeqr,
        co.marquecompteur,
        c.nom,
        c.prenom,
        c.ville
      FROM contract co
      LEFT JOIN client c ON co.idclient = c.idclient
      ORDER BY co.idcontract
    `;
    
    const summary = await client.query(summaryQuery);
    console.log('\n📋 Résumé des contrats:');
    summary.rows.forEach(contract => {
      console.log(`   - ${contract.codeqr}: ${contract.nom} ${contract.prenom} (${contract.ville}) - ${contract.marquecompteur}`);
    });
    
    client.release();
    console.log('\n✅ Données de test prêtes !');
    
  } catch (error) {
    console.error('❌ Erreur lors de l\'ajout des données de test:', error);
  } finally {
    await pool.end();
  }
}

addTestContracts();
