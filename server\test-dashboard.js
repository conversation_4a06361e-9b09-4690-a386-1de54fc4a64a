require('dotenv').config();
const express = require('express');
const cors = require('cors');
const { Pool } = require('pg');

const app = express();
const PORT = 3002;

// Middleware
app.use(cors());
app.use(express.json());

// Configuration de la base de données
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'Facturation',
  password: process.env.DB_PASSWORD || '123456',
  port: process.env.DB_PORT || 5432,
});

// Route de test
app.get('/', (req, res) => {
  res.json({
    message: 'Serveur TechnicianDashboard fonctionnel',
    timestamp: new Date().toISOString(),
    database: 'Facutration',
    status: 'OK'
  });
});

// Route pour tester la base de données
app.get('/api/database/schema', async (req, res) => {
  try {
    const client = await pool.connect();
    
    // Récupérer les tables
    const tablesResult = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
      ORDER BY table_name
    `);
    
    // Compter les enregistrements
    const counts = {};
    for (const table of tablesResult.rows) {
      const countResult = await client.query(`SELECT COUNT(*) as count FROM ${table.table_name}`);
      counts[table.table_name] = parseInt(countResult.rows[0].count);
    }
    
    client.release();
    
    res.json({
      success: true,
      message: 'Schéma de base de données récupéré',
      database: 'Facturation',
      tables: tablesResult.rows.map(row => row.table_name),
      counts: counts
    });
  } catch (err) {
    console.error('Erreur DB:', err);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// Route pour récupérer une table spécifique
app.get('/api/table/:tableName', async (req, res) => {
  try {
    const tableName = req.params.tableName;
    const client = await pool.connect();
    
    const result = await client.query(`SELECT * FROM ${tableName} LIMIT 100`);
    client.release();
    
    res.json({
      success: true,
      message: `Données de la table ${tableName} récupérées`,
      table: tableName,
      count: result.rows.length,
      data: result.rows
    });
  } catch (err) {
    console.error('Erreur table:', err);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// Route pour le dashboard du technicien
app.get('/api/technician/dashboard/:techId', async (req, res) => {
  try {
    const techId = req.params.techId;
    const client = await pool.connect();

    // Récupérer les informations du technicien
    const techResult = await client.query('SELECT * FROM utilisateur WHERE idtech = $1', [techId]);

    // Récupérer les statistiques
    const statsQueries = {
      clients: 'SELECT COUNT(*) as count FROM client',
      contrats: 'SELECT COUNT(*) as count FROM contract',
      consommations: 'SELECT COUNT(*) as count FROM consommation WHERE idtech = $1',
      factures: 'SELECT COUNT(*) as count FROM facture'
    };

    const stats = {};
    for (const [key, query] of Object.entries(statsQueries)) {
      const result = key === 'consommations'
        ? await client.query(query, [techId])
        : await client.query(query);
      stats[key] = parseInt(result.rows[0].count);
    }

    client.release();

    res.json({
      success: true,
      message: 'Dashboard du technicien récupéré',
      technician: techResult.rows[0] || null,
      stats: stats
    });
  } catch (err) {
    console.error('Erreur dashboard:', err);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// Route pour scanner QR code
app.get('/api/scan/:qrCode', async (req, res) => {
  try {
    const qrCode = req.params.qrCode;
    const client = await pool.connect();

    // Rechercher le client par code QR (supposons que c'est l'ID client)
    const result = await client.query('SELECT * FROM client WHERE idclient = $1', [qrCode]);
    client.release();

    if (result.rows.length > 0) {
      res.json({
        success: true,
        message: 'Client trouvé via QR code',
        data: result.rows[0]
      });
    } else {
      res.json({
        success: false,
        message: 'Aucun client trouvé pour ce QR code'
      });
    }
  } catch (err) {
    console.error('Erreur scan QR:', err);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// Route pour ajouter une consommation
app.post('/api/consommations', async (req, res) => {
  try {
    const { consommationActuelle, idContract, idTech } = req.body;
    const client = await pool.connect();

    const result = await client.query(
      'INSERT INTO consommation (consommationactuelle, idcontract, idtech, dateconsommation) VALUES ($1, $2, $3, NOW()) RETURNING *',
      [consommationActuelle, idContract, idTech]
    );

    client.release();

    res.json({
      success: true,
      message: 'Consommation ajoutée avec succès',
      data: result.rows[0]
    });
  } catch (err) {
    console.error('Erreur ajout consommation:', err);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// Démarrage du serveur
app.listen(PORT, () => {
  console.log(`✅ Serveur TechnicianDashboard de test démarré sur http://localhost:${PORT}`);
  console.log('📡 Routes:');
  console.log('  - GET / (test)');
  console.log('  - GET /api/database/schema (schéma DB)');
  console.log('  - GET /api/table/:tableName (données table)');
  console.log('  - GET /api/technician/dashboard/:techId (dashboard)');
});
