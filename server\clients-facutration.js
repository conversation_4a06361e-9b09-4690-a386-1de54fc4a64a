require('dotenv').config();
const express = require('express');
const cors = require('cors');
const { Pool } = require('pg');

const app = express();
const PORT = 3003;

// Middleware
app.use(cors());
app.use(express.json());

// Configuration de la base de données Facutration
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'Facutration',
  password: process.env.DB_PASSWORD || '123456',
  port: process.env.DB_PORT || 5432,
});

console.log('🚀 Démarrage du serveur Clients Facutration...');

// Test de connexion
async function testConnection() {
  try {
    console.log('🔍 Test de connexion à Facutration...');
    const client = await pool.connect();
    console.log('✅ Connexion réussie !');
    
    const result = await client.query('SELECT current_database()');
    console.log(`📊 Base de données: ${result.rows[0].current_database}`);
    
    client.release();
    return true;
  } catch (error) {
    console.error('❌ Erreur de connexion:', error.message);
    return false;
  }
}

// Route de test
app.get('/', (req, res) => {
  res.json({
    message: 'Serveur Clients Facutration fonctionnel',
    timestamp: new Date().toISOString(),
    database: 'Facutration',
    status: 'OK'
  });
});

// Route pour récupérer tous les clients
app.get('/api/clients', async (req, res) => {
  try {
    const client = await pool.connect();
    const result = await client.query(`
      SELECT c.*, s.nom as secteur_nom 
      FROM client c 
      LEFT JOIN secteur s ON c.ids = s.ids 
      ORDER BY c.idclient
    `);
    client.release();
    
    res.json({
      success: true,
      clients: result.rows,
      count: result.rows.length,
      database: 'Facutration'
    });
  } catch (error) {
    console.error('Erreur API clients:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Route pour récupérer un client par ID
app.get('/api/clients/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const client = await pool.connect();
    const result = await client.query(`
      SELECT c.*, s.nom as secteur_nom 
      FROM client c 
      LEFT JOIN secteur s ON c.ids = s.ids 
      WHERE c.idclient = $1
    `, [id]);
    client.release();
    
    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Client non trouvé'
      });
    }
    
    res.json({
      success: true,
      client: result.rows[0]
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Route pour récupérer les clients par secteur
app.get('/api/clients/secteur/:secteurId', async (req, res) => {
  try {
    const { secteurId } = req.params;
    const client = await pool.connect();
    const result = await client.query(`
      SELECT c.*, s.nom as secteur_nom 
      FROM client c 
      LEFT JOIN secteur s ON c.ids = s.ids 
      WHERE c.ids = $1
      ORDER BY c.nom, c.prenom
    `, [secteurId]);
    client.release();
    
    res.json({
      success: true,
      clients: result.rows,
      count: result.rows.length,
      secteurId: secteurId
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Route pour les statistiques des clients
app.get('/api/clients/stats', async (req, res) => {
  try {
    const client = await pool.connect();
    
    // Nombre total de clients
    const totalResult = await client.query('SELECT COUNT(*) as total FROM client');
    
    // Clients par secteur
    const secteurResult = await client.query(`
      SELECT s.nom as secteur, COUNT(c.idclient) as nombre_clients
      FROM secteur s
      LEFT JOIN client c ON s.ids = c.ids
      GROUP BY s.ids, s.nom
      ORDER BY nombre_clients DESC
    `);
    
    client.release();
    
    res.json({
      success: true,
      stats: {
        total_clients: parseInt(totalResult.rows[0].total),
        clients_par_secteur: secteurResult.rows
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Démarrage du serveur
async function startServer() {
  // Test de connexion avant démarrage
  const connected = await testConnection();
  
  if (connected) {
    app.listen(PORT, () => {
      console.log(`✅ Serveur Clients démarré sur http://localhost:${PORT}`);
      console.log(`📊 Base de données: Facutration`);
      console.log('\n📡 Routes disponibles:');
      console.log('  - GET  / (test)');
      console.log('  - GET  /api/clients (tous les clients)');
      console.log('  - GET  /api/clients/:id (client par ID)');
      console.log('  - GET  /api/clients/secteur/:secteurId (clients par secteur)');
      console.log('  - GET  /api/clients/stats (statistiques)');
    });
  } else {
    console.error('❌ Impossible de démarrer le serveur - problème de connexion DB');
    process.exit(1);
  }
}

startServer();
