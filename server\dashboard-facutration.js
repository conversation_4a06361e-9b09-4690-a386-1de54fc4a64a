require('dotenv').config();
const express = require('express');
const cors = require('cors');
const { Pool } = require('pg');

const app = express();
const PORT = 3002;

// Middleware
app.use(cors());
app.use(express.json());

// Configuration de la base de données Facutration
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'Facutration',
  password: process.env.DB_PASSWORD || '123456',
  port: process.env.DB_PORT || 5432,
});

console.log('🚀 Démarrage du serveur Dashboard Facutration...');

// Test de connexion
async function testConnection() {
  try {
    console.log('🔍 Test de connexion à Facutration...');
    const client = await pool.connect();
    console.log('✅ Connexion réussie !');
    
    const result = await client.query('SELECT current_database()');
    console.log(`📊 Base de données: ${result.rows[0].current_database}`);
    
    client.release();
    return true;
  } catch (error) {
    console.error('❌ Erreur de connexion:', error.message);
    return false;
  }
}

// Route de test
app.get('/', (req, res) => {
  res.json({
    message: 'Serveur Dashboard Facutration fonctionnel',
    timestamp: new Date().toISOString(),
    database: 'Facutration',
    status: 'OK'
  });
});

// Route pour tester la base de données
app.get('/api/test-db', async (req, res) => {
  try {
    const client = await pool.connect();
    const result = await client.query('SELECT current_database(), NOW()');
    client.release();
    
    res.json({
      success: true,
      database: result.rows[0].current_database,
      timestamp: result.rows[0].now
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Route pour récupérer les données d'une table
app.get('/api/table/:tableName', async (req, res) => {
  try {
    const { tableName } = req.params;
    const client = await pool.connect();
    
    // Vérifier que la table existe
    const tableExists = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = $1
      )
    `, [tableName]);
    
    if (!tableExists.rows[0].exists) {
      client.release();
      return res.status(404).json({
        success: false,
        error: `Table '${tableName}' n'existe pas`
      });
    }
    
    // Récupérer les données
    const result = await client.query(`SELECT * FROM ${tableName} LIMIT 100`);
    client.release();
    
    res.json({
      success: true,
      table: tableName,
      data: result.rows,
      count: result.rows.length
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Route pour lister toutes les tables
app.get('/api/tables', async (req, res) => {
  try {
    const client = await pool.connect();
    const result = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
      ORDER BY table_name
    `);
    client.release();
    
    res.json({
      success: true,
      tables: result.rows.map(row => row.table_name)
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Route pour scanner un QR code
app.get('/api/scan/:qrCode', async (req, res) => {
  try {
    const { qrCode } = req.params;
    const client = await pool.connect();

    // Rechercher le contrat par QR code, puis récupérer les infos client
    const result = await client.query(`
      SELECT
        ct.*,
        cl.nom,
        cl.prenom,
        cl.adresse,
        cl.ville,
        cl.tel,
        cl.email,
        s.nom as secteur_nom
      FROM contract ct
      JOIN client cl ON ct.idclient = cl.idclient
      LEFT JOIN secteur s ON cl.ids = s.ids
      WHERE ct.codeqr = $1
    `, [qrCode]);

    client.release();

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Contrat non trouvé pour ce QR code'
      });
    }

    res.json({
      success: true,
      data: result.rows[0],
      message: 'QR Code scanné avec succès'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Route pour ajouter une consommation
app.post('/api/consommations', async (req, res) => {
  try {
    const { idcont, consommationpre, consommationactuelle, idtech, idtranch, jours, periode } = req.body;
    const client = await pool.connect();

    const result = await client.query(`
      INSERT INTO consommation (consommationpre, consommationactuelle, idcont, idtech, idtranch, jours, periode, status)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      RETURNING *
    `, [
      consommationpre || 0,
      consommationactuelle,
      idcont,
      idtech,
      idtranch || 1,
      jours || 30,
      periode || new Date().toISOString().slice(0, 7), // YYYY-MM format
      'active'
    ]);

    client.release();

    res.json({
      success: true,
      data: result.rows[0],
      message: 'Consommation ajoutée avec succès'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Route pour le dashboard du technicien
app.get('/api/technician/dashboard/:techId', async (req, res) => {
  try {
    const { techId } = req.params;
    const client = await pool.connect();

    // Récupérer les dernières consommations avec les bonnes colonnes
    const consommationsResult = await client.query(`
      SELECT
        co.*,
        cl.nom,
        cl.prenom,
        s.nom as secteur_nom,
        ct.codeqr,
        ct.marquecompteur
      FROM consommation co
      LEFT JOIN contract ct ON co.idcont = ct.idcontract
      LEFT JOIN client cl ON ct.idclient = cl.idclient
      LEFT JOIN secteur s ON cl.ids = s.ids
      ORDER BY co.idcons DESC
      LIMIT 10
    `);

    // Statistiques générales
    const statsResult = await client.query(`
      SELECT
        COUNT(DISTINCT cl.idclient) as total_clients,
        COUNT(co.idcons) as total_consommations,
        AVG(co.consommationactuelle) as consommation_moyenne,
        COUNT(ct.idcontract) as total_contrats
      FROM client cl
      LEFT JOIN contract ct ON cl.idclient = ct.idclient
      LEFT JOIN consommation co ON ct.idcontract = co.idcont
    `);

    client.release();

    res.json({
      success: true,
      data: {
        statistiques: {
          dernieres_consommations: consommationsResult.rows,
          stats_generales: statsResult.rows[0]
        }
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Démarrage du serveur
async function startServer() {
  // Test de connexion avant démarrage
  const connected = await testConnection();

  if (connected) {
    app.listen(PORT, () => {
      console.log(`✅ Serveur Dashboard démarré sur http://localhost:${PORT}`);
      console.log(`📊 Base de données: Facutration`);
      console.log('\n📡 Routes disponibles:');
      console.log('  - GET  / (test)');
      console.log('  - GET  /api/test-db (test DB)');
      console.log('  - GET  /api/tables (lister tables)');
      console.log('  - GET  /api/table/:tableName (données table)');
      console.log('  - GET  /api/scan/:qrCode (scanner QR)');
      console.log('  - POST /api/consommations (ajouter consommation)');
      console.log('  - GET  /api/technician/dashboard/:techId (dashboard tech)');
    });
  } else {
    console.error('❌ Impossible de démarrer le serveur - problème de connexion DB');
    process.exit(1);
  }
}

startServer();
