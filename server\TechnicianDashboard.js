require('dotenv').config();
const express = require('express');
const cors = require('cors');
const { Pool } = require('pg');

const app = express();

// Middleware
app.use(cors());
app.use(express.json());

// Configuration de la base de données Facutration
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'Facutration',
  password: process.env.DB_PASSWORD || '123456',
  port: process.env.DB_PORT || 5432,
});

// Variables globales pour stocker les informations de la base
let databaseTables = [];
let databaseSchema = {};

// ==================== DÉTECTION AUTOMATIQUE DE LA BASE DE DONNÉES ====================

// Fonction pour détecter toutes les tables de la base de données
async function detectDatabaseTables() {
  try {
    console.log('🔍 Détection des tables de la base de données...');

    const result = await pool.query(`
      SELECT
        table_name,
        column_name,
        data_type,
        is_nullable,
        column_default
      FROM information_schema.columns
      WHERE table_schema = 'public'
      ORDER BY table_name, ordinal_position
    `);

    // Organiser les colonnes par table
    const tableSchema = {};
    result.rows.forEach(row => {
      if (!tableSchema[row.table_name]) {
        tableSchema[row.table_name] = [];
      }
      tableSchema[row.table_name].push({
        name: row.column_name,
        type: row.data_type,
        nullable: row.is_nullable === 'YES',
        default: row.column_default
      });
    });

    databaseSchema = tableSchema;
    databaseTables = Object.keys(tableSchema);

    console.log(`✅ ${databaseTables.length} tables détectées:`, databaseTables);

    // Afficher le schéma détaillé
    for (const [tableName, columns] of Object.entries(tableSchema)) {
      console.log(`\n📋 Table: ${tableName}`);
      columns.forEach(col => {
        console.log(`   - ${col.name} (${col.type})`);
      });
    }

    return tableSchema;
  } catch (error) {
    console.error('❌ Erreur lors de la détection des tables:', error);
    return {};
  }
}

// Fonction pour compter les enregistrements dans chaque table
async function getTableCounts() {
  try {
    console.log('\n📊 Comptage des enregistrements par table...');

    const counts = {};
    for (const tableName of databaseTables) {
      try {
        const result = await pool.query(`SELECT COUNT(*) as count FROM ${tableName}`);
        counts[tableName] = parseInt(result.rows[0].count);
        console.log(`   - ${tableName}: ${counts[tableName]} enregistrement(s)`);
      } catch (error) {
        console.log(`   - ${tableName}: ❌ Erreur de lecture`);
        counts[tableName] = 0;
      }
    }

    return counts;
  } catch (error) {
    console.error('❌ Erreur lors du comptage:', error);
    return {};
  }
}

// ==================== ROUTES DYNAMIQUES POUR TOUTES LES TABLES ====================

// Route pour obtenir le schéma complet de la base de données
app.get('/api/database/schema', async (req, res) => {
  try {
    console.log('📥 Requête GET /api/database/schema');

    const schema = await detectDatabaseTables();
    const counts = await getTableCounts();

    res.json({
      success: true,
      data: {
        tables: databaseTables,
        schema: schema,
        counts: counts,
        total_tables: databaseTables.length
      }
    });
  } catch (error) {
    console.error('❌ Erreur schema:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération du schéma',
      error: error.message
    });
  }
});

// Route dynamique pour consulter n'importe quelle table
app.get('/api/table/:tableName', async (req, res) => {
  try {
    const { tableName } = req.params;
    const { limit = 100, offset = 0 } = req.query;

    console.log(`📥 Requête GET /api/table/${tableName}`);

    // Vérifier que la table existe
    if (!databaseTables.includes(tableName.toLowerCase())) {
      return res.status(404).json({
        success: false,
        message: `Table '${tableName}' non trouvée`,
        available_tables: databaseTables
      });
    }

    // Récupérer les données de la table
    const result = await pool.query(`
      SELECT * FROM ${tableName}
      ORDER BY 1
      LIMIT $1 OFFSET $2
    `, [limit, offset]);

    // Compter le total d'enregistrements
    const countResult = await pool.query(`SELECT COUNT(*) as total FROM ${tableName}`);
    const total = parseInt(countResult.rows[0].total);

    console.log(`✅ ${result.rows.length} enregistrements récupérés de ${tableName}`);

    res.json({
      success: true,
      data: result.rows,
      pagination: {
        total: total,
        limit: parseInt(limit),
        offset: parseInt(offset),
        has_more: (parseInt(offset) + parseInt(limit)) < total
      },
      table_info: {
        name: tableName,
        columns: databaseSchema[tableName] || []
      }
    });

  } catch (error) {
    console.error(`❌ Erreur table ${req.params.tableName}:`, error);
    res.status(500).json({
      success: false,
      message: `Erreur lors de la consultation de la table ${req.params.tableName}`,
      error: error.message
    });
  }
});

// Route pour exécuter une requête SQL personnalisée (avec sécurité)
app.post('/api/query', async (req, res) => {
  try {
    const { query, params = [] } = req.body;
    console.log('📥 Requête POST /api/query:', query);

    // Sécurité: autoriser seulement les requêtes SELECT
    if (!query.trim().toLowerCase().startsWith('select')) {
      return res.status(400).json({
        success: false,
        message: 'Seules les requêtes SELECT sont autorisées'
      });
    }

    const result = await pool.query(query, params);

    console.log(`✅ Requête exécutée: ${result.rows.length} résultats`);

    res.json({
      success: true,
      data: result.rows,
      count: result.rows.length,
      query: query
    });

  } catch (error) {
    console.error('❌ Erreur requête SQL:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de l\'exécution de la requête',
      error: error.message
    });
  }
});

// ==================== ROUTES SPÉCIALISÉES POUR TECHNICIAN DASHBOARD ====================

// Route pour obtenir les statistiques du tableau de bord (dynamiques depuis la base)
app.get('/api/dashboard/stats', async (req, res) => {
  try {
    console.log('📥 Requête GET /api/dashboard/stats');

    // Exécuter toutes les requêtes en parallèle pour optimiser les performances
    const statsQueries = await Promise.all([
      // Total des interventions (consommations relevées)
      pool.query('SELECT COUNT(*) as count FROM consommation'),

      // Interventions en cours (consommations avec status "En cours")
      pool.query('SELECT COUNT(*) as count FROM consommation WHERE status = $1', ['En cours']),

      // Total des clients
      pool.query('SELECT COUNT(*) as count FROM client'),

      // Relevés d'aujourd'hui (consommations de la période actuelle)
      pool.query(`
        SELECT COUNT(*) as count
        FROM consommation
        WHERE periode = $1
      `, [new Date().toISOString().slice(0, 7)]) // Format YYYY-MM
    ]);

    const stats = {
      interventions: parseInt(statsQueries[0].rows[0].count),
      interventionsEnCours: parseInt(statsQueries[1].rows[0].count),
      clients: parseInt(statsQueries[2].rows[0].count),
      relevesAujourdhui: parseInt(statsQueries[3].rows[0].count)
    };

    console.log('✅ Statistiques calculées:', stats);

    res.json({
      success: true,
      data: stats,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Erreur lors du calcul des statistiques:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors du calcul des statistiques',
      error: error.message
    });
  }
});

// Route pour obtenir les données du dashboard technicien
app.get('/api/technician/dashboard/:techId', async (req, res) => {
  try {
    const { techId } = req.params;
    console.log(`📥 Requête GET /api/technician/dashboard/${techId}`);

    // Récupérer les informations du technicien
    const techResult = await pool.query(
      'SELECT * FROM utilisateur WHERE idtech = $1',
      [techId]
    );

    if (techResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Technicien non trouvé'
      });
    }

    const technicien = techResult.rows[0];

   
    const statsQueries = await Promise.all([
      // Nombre de consommations relevées par ce technicien
      pool.query('SELECT COUNT(*) as count FROM consommation WHERE idtech = $1', [techId]),

      // Nombre de clients dans sa zone (si applicable)
      pool.query('SELECT COUNT(*) as count FROM client'),

      // Dernières consommations relevées
      pool.query(`
        SELECT c.*, cl.nom as client_nom, cl.prenom as client_prenom, cont.codeqr
        FROM consommation c
        LEFT JOIN contract cont ON c.idcont = cont.idcontract
        LEFT JOIN client cl ON cont.idclient = cl.idclient
        WHERE c.idtech = $1
        ORDER BY c.periode DESC
        LIMIT 10
      `, [techId]),

      // Contrats liés aux consommations du technicien (remplace factures)
      pool.query(`
        SELECT cont.*, cl.nom as client_nom, cl.prenom as client_prenom
        FROM contract cont
        LEFT JOIN consommation cons ON cons.idcont = cont.idcontract
        LEFT JOIN client cl ON cont.idclient = cl.idclient
        WHERE cons.idtech = $1
        ORDER BY cont.datecontract DESC
        LIMIT 10
      `, [techId])
    ]);

    const dashboardData = {
      technicien: technicien,
      statistiques: {
        consommations_relevees: parseInt(statsQueries[0].rows[0].count),
        total_clients: parseInt(statsQueries[1].rows[0].count),
        dernieres_consommations: statsQueries[2].rows,
        contrats_associes: statsQueries[3].rows
      }
    };

    console.log(`✅ Dashboard généré pour technicien ${techId}`);

    res.json({
      success: true,
      data: dashboardData
    });

  } catch (error) {
    console.error('❌ Erreur dashboard technicien:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la génération du dashboard',
      error: error.message
    });
  }
});

// Route pour scanner un QR code
app.get('/api/scan/:qrCode', async (req, res) => {
  try {
    const { qrCode } = req.params;
    console.log(`📥 Scan QR Code: ${qrCode}`);

    const result = await pool.query(`
      SELECT
        cont.*,
        cl.nom as client_nom,
        cl.prenom as client_prenom,
        cl.adresse,
        cl.tel,
        cl.email
      FROM contract cont
      LEFT JOIN client cl ON cont.idclient = cl.idclient
      WHERE cont.codeqr = $1
    `, [qrCode]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'QR Code non trouvé'
      });
    }

    console.log(`✅ QR Code trouvé: ${result.rows[0].client_nom}`);

    res.json({
      success: true,
      data: result.rows[0]
    });

  } catch (error) {
    console.error('❌ Erreur scan QR:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors du scan QR',
      error: error.message
    });
  }
});

// ==================== INITIALISATION ET DÉMARRAGE ====================

// Test de connexion et détection automatique au démarrage
async function initializeServer() {
  try {
    console.log('🚀 Initialisation du serveur TechnicianDashboard...');

    // Test de connexion
    const client = await pool.connect();
    console.log('✅ Connexion à la base de données réussie');
    client.release();

    // Détection automatique des tables
    await detectDatabaseTables();
    await getTableCounts();

    console.log('\n✅ Serveur initialisé avec succès!');

  } catch (error) {
    console.error('❌ Erreur d\'initialisation:', error);
    process.exit(1);
  }
}

// Route de test
app.get('/', (req, res) => {
  res.json({
    message: 'Serveur TechnicianDashboard fonctionnel',
    timestamp: new Date().toISOString(),
    database: process.env.DB_NAME || 'Facturation',
    tables_detected: databaseTables.length,
    tables: databaseTables
  });
});

// Route pour ajouter une nouvelle consommation
app.post('/api/consommation', async (req, res) => {
  try {
    console.log('📥 Requête POST /api/consommation:', req.body);
    const {
      consommationpre,
      consommationactuelle,
      idcont,
      idtech,
      idtranch,
      jours,
      periode,
      status = 'En cours'
    } = req.body;

    // Validation des champs requis
    if (!consommationpre || !consommationactuelle || !idcont || !idtech) {
      return res.status(400).json({
        success: false,
        message: 'Les champs consommationpre, consommationactuelle, idcont et idtech sont requis'
      });
    }

    // Validation que consommationactuelle >= consommationpre
    if (parseInt(consommationactuelle) < parseInt(consommationpre)) {
      return res.status(400).json({
        success: false,
        message: 'La consommation actuelle ne peut pas être inférieure à la consommation précédente'
      });
    }

    // Insérer la nouvelle consommation
    const insertQuery = `
      INSERT INTO consommation (
        consommationpre,
        consommationactuelle,
        idcont,
        idtech,
        idtranch,
        jours,
        periode,
        status
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      RETURNING *
    `;

    const values = [
      parseInt(consommationpre),
      parseInt(consommationactuelle),
      parseInt(idcont),
      parseInt(idtech),
      idtranch ? parseInt(idtranch) : null,
      jours ? parseInt(jours) : null,
      periode || new Date().toISOString().slice(0, 7), // Format YYYY-MM par défaut
      status
    ];

    const result = await pool.query(insertQuery, values);
    const newConsommation = result.rows[0];

    console.log('✅ Nouvelle consommation ajoutée:', newConsommation);

    res.json({
      success: true,
      message: 'Consommation ajoutée avec succès',
      data: newConsommation
    });

  } catch (error) {
    console.error('❌ Erreur lors de l\'ajout de la consommation:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de l\'ajout de la consommation',
      error: error.message
    });
  }
});

// Route pour récupérer les contrats avec informations client (pour le formulaire de consommation)
app.get('/api/contracts', async (req, res) => {
  try {
    console.log('📥 Requête GET /api/contracts');

    const query = `
      SELECT
        cont.idcontract,
        cont.codeqr,
        cont.datecontract,
        cl.idclient,
        cl.nom,
        cl.prenom,
        cl.adresse,
        cl.tel,
        s.nom as nomsecteur
      FROM contract cont
      LEFT JOIN client cl ON cont.idclient = cl.idclient
      LEFT JOIN secteur s ON cl.ids = s.ids
      ORDER BY cl.nom, cl.prenom
    `;

    const result = await pool.query(query);

    console.log(`✅ ${result.rows.length} contrats récupérés`);

    res.json({
      success: true,
      data: result.rows,
      count: result.rows.length
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération des contrats:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la récupération des contrats',
      error: error.message
    });
  }
});

// Route de connexion
app.post('/login', async (req, res) => {
  console.log('📥 Requête de connexion reçue:', req.body);
  const { email, motDepass } = req.body;

  try {
    // Validation des champs requis
    if (!email || !motDepass) {
      return res.status(400).json({
        success: false,
        message: "Email et mot de passe requis"
      });
    }

    console.log('🔍 Recherche de l\'utilisateur:', email);

    // Recherche de l'utilisateur dans la table utilisateur
    const userResult = await pool.query(
      'SELECT * FROM utilisateur WHERE email = $1',
      [email]
    );

    if (userResult.rows.length === 0) {
      console.log('❌ Aucun utilisateur trouvé avec cet email:', email);
      return res.status(401).json({
        success: false,
        message: 'Email ou mot de passe incorrect'
      });
    }

    const user = userResult.rows[0];
    console.log('✅ Utilisateur trouvé:', {
      email: user.email,
      role: user.role
    });

    // Vérification du mot de passe
    if (motDepass !== user.motdepass) {
      console.log('❌ Mot de passe incorrect pour:', email);
      return res.status(401).json({
        success: false,
        message: 'Email ou mot de passe incorrect'
      });
    }

    // Connexion réussie
    console.log('✅ Connexion réussie pour:', email, '- Rôle:', user.role);
    res.json({
      success: true,
      message: 'Connexion réussie',
      user: {
        id: user.idtech || user.id,
        nom: user.nom,
        prenom: user.prenom,
        email: user.email,
        role: user.role
      }
    });

  } catch (error) {
    console.error('❌ Erreur de connexion:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur',
      error: error.message
    });
  }
});

// Démarrer le serveur
const PORT = process.env.PORT || 3002;

initializeServer().then(() => {
  app.listen(PORT, () => {
    console.log(`\n🚀 Serveur TechnicianDashboard démarré sur http://localhost:${PORT}`);
    console.log(`📊 Base de données: ${process.env.DB_NAME || 'Facturation'}`);
    console.log(`📋 Tables détectées: ${databaseTables.length}`);
    console.log('\n📡 Routes disponibles:');
    console.log('  - GET  / (test)');
    console.log('  - GET  /api/database/schema (schéma complet)');
    console.log('  - GET  /api/table/:tableName (consultation table)');
    console.log('  - POST /api/query (requête SQL)');
    console.log('  - GET  /api/dashboard/stats (statistiques dynamiques)');
    console.log('  - GET  /api/contracts (liste des contrats actifs)');
    console.log('  - POST /api/consommation (ajouter consommation)');
    console.log('  - GET  /api/technician/dashboard/:techId');
    console.log('  - GET  /api/scan/:qrCode');
  });
}).catch(err => {
  console.error('❌ Erreur lors du démarrage du serveur:', err.message);
  process.exit(1);
});

module.exports = app;