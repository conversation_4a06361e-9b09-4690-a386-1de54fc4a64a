// Script de test pour TechnicianDashboard.js
// Ce script teste la détection automatique de la base de données

const axios = require('axios').default;

const BASE_URL = 'http://localhost:3002';

// Fonction pour tester une route
async function testRoute(method, endpoint, data = null) {
  try {
    console.log(`\n🔍 Test ${method.toUpperCase()} ${endpoint}`);
    
    let response;
    if (method === 'GET') {
      response = await axios.get(`${BASE_URL}${endpoint}`);
    } else if (method === 'POST') {
      response = await axios.post(`${BASE_URL}${endpoint}`, data);
    }
    
    console.log(`✅ Status: ${response.status}`);
    console.log(`📊 Données reçues:`, JSON.stringify(response.data, null, 2));
    
    return response.data;
  } catch (error) {
    console.error(`❌ Erreur ${method} ${endpoint}:`, error.response?.data || error.message);
    return null;
  }
}

// Tests principaux
async function runTests() {
  console.log('🚀 Démarrage des tests pour TechnicianDashboard.js');
  console.log('📍 URL de base:', BASE_URL);
  
  // Test 1: Route de base
  console.log('\n=== TEST 1: Route de base ===');
  await testRoute('GET', '/');
  
  // Test 2: Schéma de la base de données
  console.log('\n=== TEST 2: Détection du schéma de la base ===');
  const schemaData = await testRoute('GET', '/api/database/schema');
  
  if (schemaData && schemaData.success) {
    console.log(`\n📋 Tables détectées: ${schemaData.data.total_tables}`);
    schemaData.data.tables.forEach(table => {
      console.log(`   - ${table} (${schemaData.data.counts[table]} enregistrements)`);
    });
  }
  
  // Test 3: Consultation de tables spécifiques
  console.log('\n=== TEST 3: Consultation des tables ===');
  
  const tablesToTest = ['client', 'utilisateur', 'contract', 'consommation', 'facture'];
  
  for (const table of tablesToTest) {
    console.log(`\n--- Test table: ${table} ---`);
    await testRoute('GET', `/api/table/${table}?limit=3`);
  }
  
  // Test 4: Requête SQL personnalisée
  console.log('\n=== TEST 4: Requête SQL personnalisée ===');
  await testRoute('POST', '/api/query', {
    query: 'SELECT COUNT(*) as total_clients FROM client'
  });
  
  // Test 5: Dashboard technicien (si des techniciens existent)
  console.log('\n=== TEST 5: Dashboard technicien ===');
  await testRoute('GET', '/api/technician/dashboard/1');
  
  // Test 6: Scan QR Code (test avec un code fictif)
  console.log('\n=== TEST 6: Scan QR Code ===');
  await testRoute('GET', '/api/scan/QR123456');
  
  console.log('\n✅ Tests terminés!');
}

// Fonction pour vérifier que le serveur est démarré
async function checkServerStatus() {
  try {
    const response = await axios.get(BASE_URL);
    console.log('✅ Serveur TechnicianDashboard accessible');
    return true;
  } catch (error) {
    console.error('❌ Serveur TechnicianDashboard non accessible');
    console.error('💡 Assurez-vous que le serveur est démarré avec: node server/TechnicianDashboard.js');
    return false;
  }
}

// Démarrer les tests
async function main() {
  console.log('🔧 Vérification du statut du serveur...');
  
  const serverRunning = await checkServerStatus();
  
  if (serverRunning) {
    await runTests();
  } else {
    console.log('\n📝 Instructions pour démarrer le serveur:');
    console.log('1. Ouvrez un terminal');
    console.log('2. Naviguez vers le dossier du projet');
    console.log('3. Exécutez: node server/TechnicianDashboard.js');
    console.log('4. Attendez que le serveur soit initialisé');
    console.log('5. Relancez ce script de test');
  }
}

// Gestion des erreurs globales
process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Erreur non gérée:', reason);
});

// Démarrer
main().catch(console.error);
