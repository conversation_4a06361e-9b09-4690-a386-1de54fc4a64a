require('dotenv').config();
const express = require('express');
const cors = require('cors');
const { Pool } = require('pg');

const app = express();

// Middleware
app.use(cors());
app.use(express.json());

// Configuration de la base de données Facturation
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'Facturation',
  password: process.env.DB_PASSWORD || '123456',
  port: process.env.DB_PORT || 5432,
});

// Test de connexion à la base de données
pool.connect((err, client, release) => {
  if (err) {
    console.error('❌ Erreur de connexion à la base de données:', err);
  } else {
    console.log('✅ Connexion à la base de données Facturation réussie');
    release();
  }
});

// Route de test
app.get('/', (req, res) => {
  res.json({ 
    message: 'Serveur TechnicianDashboard fonctionnel', 
    timestamp: new Date().toISOString(),
    database: 'Facturation'
  });
});

// ==================== ROUTES POUR TECHNICIAN DASHBOARD ====================

// 1. Route pour obtenir tous les clients
app.get('/api/clients', async (req, res) => {
  try {
    console.log('📥 Requête GET /api/clients');
    
    const result = await pool.query(`
      SELECT 
        c.idclient,
        c.nom,
        c.prenom,
        c.adresse,
        c.ville,
        c.tel,
        c.email,
        s.nom as secteur_nom
      FROM Client c
      LEFT JOIN Secteur s ON c.ids = s.ids
      ORDER BY c.nom, c.prenom
    `);

    console.log(`✅ ${result.rows.length} clients trouvés`);
    res.json({
      success: true,
      data: result.rows,
      count: result.rows.length
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération des clients:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des clients',
      error: error.message
    });
  }
});

// 2. Route pour obtenir les consommations
app.get('/api/consommations', async (req, res) => {
  try {
    console.log('📥 Requête GET /api/consommations');
    
    const result = await pool.query(`
      SELECT 
        cons.idcons,
        cons.consommationpre,
        cons.consommationactuelle,
        cons.jours,
        cons.periode,
        cons.status,
        cl.nom as client_nom,
        cl.prenom as client_prenom,
        cont.codeqr,
        cont.marquecompteur,
        cont.numseriecompteur,
        u.nom as technicien_nom,
        u.prenom as technicien_prenom
      FROM Consommation cons
      LEFT JOIN Contract cont ON cons.idcont = cont.idcontract
      LEFT JOIN Client cl ON cont.idclient = cl.idclient
      LEFT JOIN Utilisateur u ON cons.idtech = u.idtech
      ORDER BY cons.periode DESC
    `);

    console.log(`✅ ${result.rows.length} consommations trouvées`);
    res.json({
      success: true,
      data: result.rows,
      count: result.rows.length
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération des consommations:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des consommations',
      error: error.message
    });
  }
});

// 3. Route pour obtenir les factures
app.get('/api/factures', async (req, res) => {
  try {
    console.log('📥 Requête GET /api/factures');
    
    const result = await pool.query(`
      SELECT 
        f.idfact,
        f.date,
        f.montant,
        f.periode,
        f.reference,
        f.status,
        cl.nom as client_nom,
        cl.prenom as client_prenom,
        cons.consommationactuelle,
        cons.consommationpre
      FROM Facture f
      LEFT JOIN Consommation cons ON f.idconst = cons.idcons
      LEFT JOIN Contract cont ON cons.idcont = cont.idcontract
      LEFT JOIN Client cl ON cont.idclient = cl.idclient
      ORDER BY f.date DESC
    `);

    console.log(`✅ ${result.rows.length} factures trouvées`);
    res.json({
      success: true,
      data: result.rows,
      count: result.rows.length
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération des factures:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des factures',
      error: error.message
    });
  }
});

// 4. Route pour obtenir les contrats avec positions GPS
app.get('/api/contracts', async (req, res) => {
  try {
    console.log('📥 Requête GET /api/contracts');
    
    const result = await pool.query(`
      SELECT 
        cont.idcontract,
        cont.codeqr,
        cont.datecontract,
        cont.marquecompteur,
        cont.numseriecompteur,
        cont.posx,
        cont.posy,
        cl.nom as client_nom,
        cl.prenom as client_prenom,
        cl.adresse,
        cl.ville,
        cl.tel,
        cl.email
      FROM Contract cont
      LEFT JOIN Client cl ON cont.idclient = cl.idclient
      ORDER BY cont.datecontract DESC
    `);

    console.log(`✅ ${result.rows.length} contrats trouvés`);
    res.json({
      success: true,
      data: result.rows,
      count: result.rows.length
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération des contrats:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des contrats',
      error: error.message
    });
  }
});

// 5. Route pour obtenir les secteurs
app.get('/api/secteurs', async (req, res) => {
  try {
    console.log('📥 Requête GET /api/secteurs');
    
    const result = await pool.query(`
      SELECT 
        s.ids,
        s.nom,
        COUNT(c.idclient) as nombre_clients
      FROM Secteur s
      LEFT JOIN Client c ON s.ids = c.ids
      GROUP BY s.ids, s.nom
      ORDER BY s.nom
    `);

    console.log(`✅ ${result.rows.length} secteurs trouvés`);
    res.json({
      success: true,
      data: result.rows,
      count: result.rows.length
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération des secteurs:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des secteurs',
      error: error.message
    });
  }
});

// 6. Route pour obtenir les tranches tarifaires
app.get('/api/tranches', async (req, res) => {
  try {
    console.log('📥 Requête GET /api/tranches');
    
    const result = await pool.query(`
      SELECT 
        idtranch,
        prix,
        valeurmin,
        valeurmax
      FROM Tranch
      ORDER BY valeurmin
    `);

    console.log(`✅ ${result.rows.length} tranches trouvées`);
    res.json({
      success: true,
      data: result.rows,
      count: result.rows.length
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération des tranches:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des tranches',
      error: error.message
    });
  }
});

// 7. Route pour obtenir les utilisateurs (techniciens)
app.get('/api/utilisateurs', async (req, res) => {
  try {
    console.log('📥 Requête GET /api/utilisateurs');
    
    const result = await pool.query(`
      SELECT 
        idtech,
        nom,
        prenom,
        adresse,
        tel,
        email,
        role
      FROM Utilisateur
      ORDER BY role, nom, prenom
    `);

    console.log(`✅ ${result.rows.length} utilisateurs trouvés`);
    res.json({
      success: true,
      data: result.rows,
      count: result.rows.length
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération des utilisateurs:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des utilisateurs',
      error: error.message
    });
  }
});

// 8. Route pour obtenir un résumé des statistiques
app.get('/api/dashboard/stats', async (req, res) => {
  try {
    console.log('📥 Requête GET /api/dashboard/stats');
    
    // Exécuter plusieurs requêtes en parallèle
    const [clientsResult, consommationsResult, facturesResult, contractsResult] = await Promise.all([
      pool.query('SELECT COUNT(*) as total FROM Client'),
      pool.query('SELECT COUNT(*) as total FROM Consommation'),
      pool.query('SELECT COUNT(*) as total, SUM(montant) as total_montant FROM Facture'),
      pool.query('SELECT COUNT(*) as total FROM Contract')
    ]);

    const stats = {
      clients: parseInt(clientsResult.rows[0].total),
      consommations: parseInt(consommationsResult.rows[0].total),
      factures: {
        total: parseInt(facturesResult.rows[0].total),
        montant_total: parseFloat(facturesResult.rows[0].total_montant) || 0
      },
      contracts: parseInt(contractsResult.rows[0].total)
    };

    console.log('✅ Statistiques calculées:', stats);
    res.json({
      success: true,
      data: stats
    });

  } catch (error) {
    console.error('❌ Erreur lors du calcul des statistiques:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors du calcul des statistiques',
      error: error.message
    });
  }
});

// ==================== ROUTES POST POUR AJOUTER DES DONNÉES ====================

// 9. Route pour ajouter une nouvelle consommation
app.post('/api/consommations', async (req, res) => {
  try {
    console.log('📥 Requête POST /api/consommations:', req.body);

    const {
      consommationPre,
      consommationActuelle,
      idContract,
      idTech,
      idTranch,
      jours,
      periode,
      status
    } = req.body;

    // Validation des champs requis
    if (!consommationActuelle || !idContract || !idTech) {
      return res.status(400).json({
        success: false,
        message: 'Champs requis: consommationActuelle, idContract, idTech'
      });
    }

    const result = await pool.query(`
      INSERT INTO Consommation (
        consommationpre,
        consommationactuelle,
        idcont,
        idtech,
        idtranch,
        jours,
        periode,
        status
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      RETURNING *
    `, [
      consommationPre || 0,
      consommationActuelle,
      idContract,
      idTech,
      idTranch || 1,
      jours || 30,
      periode || new Date().toISOString().slice(0, 7), // YYYY-MM
      status || 'active'
    ]);

    console.log('✅ Nouvelle consommation ajoutée:', result.rows[0]);
    res.json({
      success: true,
      message: 'Consommation ajoutée avec succès',
      data: result.rows[0]
    });

  } catch (error) {
    console.error('❌ Erreur lors de l\'ajout de la consommation:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de l\'ajout de la consommation',
      error: error.message
    });
  }
});

// 10. Route pour scanner un QR code et obtenir les infos du contrat
app.get('/api/scan/:qrCode', async (req, res) => {
  try {
    const { qrCode } = req.params;
    console.log('📥 Requête GET /api/scan/' + qrCode);

    const result = await pool.query(`
      SELECT
        cont.idcontract,
        cont.codeqr,
        cont.marquecompteur,
        cont.numseriecompteur,
        cont.posx,
        cont.posy,
        cl.nom as client_nom,
        cl.prenom as client_prenom,
        cl.adresse,
        cl.ville,
        cl.tel,
        cl.email
      FROM Contract cont
      LEFT JOIN Client cl ON cont.idclient = cl.idclient
      WHERE cont.codeqr = $1
    `, [qrCode]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'QR Code non trouvé'
      });
    }

    console.log('✅ QR Code trouvé:', result.rows[0]);
    res.json({
      success: true,
      data: result.rows[0]
    });

  } catch (error) {
    console.error('❌ Erreur lors du scan QR:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors du scan QR',
      error: error.message
    });
  }
});

// 11. Route pour obtenir l'historique des actions d'un technicien
app.get('/api/historique/:techId', async (req, res) => {
  try {
    const { techId } = req.params;
    console.log('📥 Requête GET /api/historique/' + techId);

    const result = await pool.query(`
      SELECT
        cons.idcons,
        cons.periode,
        cons.consommationactuelle,
        cons.status,
        cl.nom as client_nom,
        cl.prenom as client_prenom,
        cont.codeqr,
        'Relevé consommation' as action_type,
        cons.periode as date_action
      FROM Consommation cons
      LEFT JOIN Contract cont ON cons.idcont = cont.idcontract
      LEFT JOIN Client cl ON cont.idclient = cl.idclient
      WHERE cons.idtech = $1
      ORDER BY cons.periode DESC
      LIMIT 50
    `, [techId]);

    console.log(`✅ ${result.rows.length} actions trouvées pour le technicien ${techId}`);
    res.json({
      success: true,
      data: result.rows,
      count: result.rows.length
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération de l\'historique:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération de l\'historique',
      error: error.message
    });
  }
});

// 12. Route pour obtenir les clients avec leurs positions GPS
app.get('/api/clients/map', async (req, res) => {
  try {
    console.log('📥 Requête GET /api/clients/map');

    const result = await pool.query(`
      SELECT
        cl.idclient,
        cl.nom,
        cl.prenom,
        cl.adresse,
        cl.ville,
        cont.posx as latitude,
        cont.posy as longitude,
        cont.codeqr,
        cont.marquecompteur
      FROM Client cl
      LEFT JOIN Contract cont ON cl.idclient = cont.idclient
      WHERE cont.posx IS NOT NULL AND cont.posy IS NOT NULL
      ORDER BY cl.nom, cl.prenom
    `);

    console.log(`✅ ${result.rows.length} clients avec positions GPS trouvés`);
    res.json({
      success: true,
      data: result.rows,
      count: result.rows.length
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération des positions clients:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des positions clients',
      error: error.message
    });
  }
});

// Démarrer le serveur
const PORT = process.env.PORT || 3002;
app.listen(PORT, () => {
  console.log(`🚀 Serveur TechnicianDashboard démarré sur http://localhost:${PORT}`);
  console.log(`📊 Base de données: ${process.env.DB_NAME || 'Facturation'}`);
  console.log(`👤 Utilisateur DB: ${process.env.DB_USER || 'postgres'}`);
  console.log('📋 Routes disponibles:');
  console.log('  GET Routes:');
  console.log('    - GET /api/clients');
  console.log('    - GET /api/consommations');
  console.log('    - GET /api/factures');
  console.log('    - GET /api/contracts');
  console.log('    - GET /api/secteurs');
  console.log('    - GET /api/tranches');
  console.log('    - GET /api/utilisateurs');
  console.log('    - GET /api/dashboard/stats');
  console.log('    - GET /api/scan/:qrCode');
  console.log('    - GET /api/historique/:techId');
  console.log('    - GET /api/clients/map');
  console.log('  POST Routes:');
  console.log('    - POST /api/consommations');
});
