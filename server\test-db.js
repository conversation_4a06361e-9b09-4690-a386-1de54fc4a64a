require('dotenv').config();
const { Pool } = require('pg');

console.log('🔧 Configuration de la base de données:');
console.log('- User:', process.env.DB_USER || 'postgres');
console.log('- Host:', process.env.DB_HOST || 'localhost');
console.log('- Database:', process.env.DB_NAME || 'Facturation');
console.log('- Port:', process.env.DB_PORT || 5432);

const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'Facutration',
  password: process.env.DB_PASSWORD || '123456',
  port: process.env.DB_PORT || 5432,
});

async function testConnection() {
  try {
    console.log('🔍 Test de connexion...');
    const client = await pool.connect();
    console.log('✅ Connexion réussie !');
    
    console.log('🔍 Test de requête...');
    const result = await client.query('SELECT COUNT(*) FROM Utilisateur');
    console.log('✅ Requête réussie ! Nombre d\'utilisateurs:', result.rows[0].count);
    
    client.release();
    await pool.end();
    console.log('✅ Test terminé avec succès');
    
  } catch (error) {
    console.error('❌ Erreur:', error.message);
    console.error('❌ Code d\'erreur:', error.code);
    process.exit(1);
  }
}

testConnection();
