/* TechnicianDashboard - Style Application Mobile avec palette bleue moderne */
  *{
    color :#000;
    background-color: #fff;
    font-size: 16px; /* Taille de base augmentée */
  }
.tech-dashboard-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: white; /* Arrière-plan blanc */
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  font-size: 16px; /* Taille de base augmentée */
  overflow: hidden;
}

/* ===== HEADER MOBILE ===== */
.tech-mobile-header {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); /* Bleu moderne */
  backdrop-filter: blur(20px);
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 4px 25px rgba(59, 130, 246, 0.3);
  border-bottom: 1px solid rgba(255,255,255,0.1);
  position: relative;
  z-index: 1000;
}

.tech-mobile-logo {
  display: flex;
  align-items: center;
  gap: 10px;
}

.tech-mobile-logo-icon {
  width: 35px;
  height: 35px;
  background: linear-gradient(45deg, #60a5fa, #3b82f6); /* Bleu clair moderne */
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: #000;
  box-shadow: 0 4px 15px rgba(96, 165, 250, 0.4);
}

.tech-mobile-logo-text {
  font-size: 22px; 
  font-weight: 700;
  color: #000; 
  letter-spacing: -0.5px;
}

.tech-mobile-user {
  display: flex;
  align-items: center;
  gap: 10px;
}

.tech-mobile-avatar {
  width: 35px;
  height: 35px;
  background: linear-gradient(45deg, #60a5fa, #3b82f6); /* Bleu clair moderne */
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #000;
  font-weight: 600;
  font-size: 16px;
  box-shadow: 0 4px 15px rgba(96, 165, 250, 0.4);
}

.tech-mobile-menu-btn {
  background: none;
  border: none;
  font-size: 24px; 
  color: #000; 
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: background-color 0.3s ease;
}

.tech-mobile-menu-btn:hover {
  background: rgba(255,255,255,0.1);
}

/* ===== NAVIGATION MOBILE BOTTOM ===== */
.tech-mobile-nav {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  padding: 10px 0;
  display: flex;
  justify-content: space-around;
  align-items: center;
  box-shadow: 0 -4px 25px rgba(59, 130, 246, 0.15);
  border-top: 2px solid #3b82f6; /* Bordure bleue */
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}

.tech-mobile-nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 8px 12px;
  background: none;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 12px;
  min-width: 60px;
  font-size: 18px; 
}

.tech-mobile-nav-item.active {
  background: linear-gradient(45deg, #3b82f6, #1d4ed8); /* Bleu pour l'élément actif */
  color: #000;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.4);
}

.tech-mobile-nav-item:not(.active) {
  color: #000;
}

.tech-mobile-nav-item:not(.active):hover {
  background: rgba(59, 130, 246, 0.1); /* Hover bleu léger */
  color: #1d4ed8; 
}

.tech-mobile-nav-icon {
  font-size: 20px;
  margin-bottom: 2px;
}

.tech-mobile-nav-label {
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* ===== STYLES POUR LES LIENS DE NAVIGATION ===== */
.tech-mobile-nav-item.nav-link {
  position: relative;
  border: 2px solid transparent;
  background: linear-gradient(white, white) padding-box,
              linear-gradient(45deg, #3b82f6, #1d4ed8) border-box;
}

.tech-mobile-nav-item.nav-link:hover {
  background: linear-gradient(rgba(59, 130, 246, 0.1), rgba(59, 130, 246, 0.1)) padding-box,
              linear-gradient(45deg, #60a5fa, #3b82f6) border-box; 
  transform: translateY(-1px);
}

.nav-link-indicator {
  font-size: 12px;
  margin-left: 4px;
  opacity: 0.7;
  transition: all 0.3s ease;
}

.tech-mobile-nav-item.nav-link:hover .nav-link-indicator {
  opacity: 1;
  transform: translateX(2px);
}

/* ===== MAIN CONTENT MOBILE ===== */
.tech-mobile-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px 15px 100px 15px;
  background: #f8fafc; 
}

/* ===== CARDS MOBILE ===== */
.tech-mobile-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  border: 1px solid rgba(255,255,255,0.2);
  transition: transform 0.3s ease;
}

.tech-mobile-card:hover {
  transform: translateY(-2px);
}

.tech-mobile-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.tech-mobile-card-title {
  font-size: 22px; 
  font-weight: 700;
  color: #1e40af;
  margin: 0;
}

.tech-mobile-card-subtitle {
  font-size: 18px; 
  color: #000;
  margin: 5px 0 0 0;
}

/* ===== STATS GRID MOBILE ===== */
.tech-mobile-stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
  margin-bottom: 20px;
}

.tech-mobile-stat-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  padding: 20px;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  border: 1px solid rgba(255,255,255,0.2);
  transition: all 0.3s ease;
}

.tech-mobile-stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0,0,0,0.15);
}

.tech-mobile-stat-icon {
  font-size: 24px;
  width: 45px;
  height: 45px;
  background: #2678dc;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #000;
  margin: 0 auto 10px auto;
  box-shadow: 0 4px 15px rgba(245, 158, 11, 0.4);
}

.tech-mobile-stat-number {
  font-size: 28px; /* Augmenté de 24px à 28px */
  font-weight: 700;
  color: #1e40af; /* Bleu foncé pour les chiffres */
  margin: 8px 0 4px 0;
}

.tech-mobile-stat-label {
  font-size: 14px; /* Augmenté de 12px à 14px */
  color: #000;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* ===== INTERVENTIONS LIST MOBILE ===== */
.tech-mobile-intervention-item {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 15px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  border: 1px solid rgba(255,255,255,0.2);
  transition: all 0.3s ease;
}

.tech-mobile-intervention-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0,0,0,0.15);
}

.tech-mobile-intervention-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.tech-mobile-intervention-client {
  font-size: 20px; 
  font-weight: 700;
  color: #1e40af; 
  margin: 0;
}

.tech-mobile-intervention-type {
  font-size: 16px; 
  color: #000;
  margin: 4px 0 0 0;
}

.tech-mobile-status-badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 15px; 
  font-weight: 600;
  text-align: center;
  color: white;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.tech-mobile-intervention-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
  margin: 15px 0;
}

.tech-mobile-detail-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.tech-mobile-detail-label {
  font-size: 16px;
  color: #000;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.tech-mobile-detail-value {
  font-size: 14px;
  color: #000; /* Gris foncé pour les valeurs */
  font-weight: 500;
}

.tech-mobile-intervention-actions {
  display: flex;
  gap: 10px;
  margin-top: 15px;
}

.tech-mobile-action-btn {
  flex: 1;
  padding: 12px;
  border: none;
  border-radius: 12px;
  font-size: 16px; /* Augmenté de 14px à 16px */
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.tech-mobile-action-btn.complete {
  background: linear-gradient(45deg, #3b82f6, #1d4ed8); /* Bleu pour compléter */
  color: white;
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.4);
}

.tech-mobile-action-btn.start {
  background: linear-gradient(45deg, #60a5fa, #3b82f6); /* Bleu clair pour démarrer */
  color: white;
  box-shadow: 0 4px 15px rgba(96, 165, 250, 0.4);
}

.tech-mobile-action-btn:hover {
  transform: translateY(-2px);
}

/* ===== PROFILE MOBILE ===== */
.tech-mobile-profile-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 25px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  border: 1px solid rgba(255,255,255,0.2);
}

.tech-mobile-profile-avatar {
  width: 80px;
  height: 80px;
  background: #2678dc;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 32px;
  font-weight: 700;
  margin: 0 auto 20px auto;
  box-shadow: 0 8px 32px rgba(245, 158, 11, 0.4);
}

.tech-mobile-profile-info {
  text-align: center;
}

.tech-mobile-profile-name {
  font-size: 24px; 
  font-weight: 700;
  color: #1e40af; 
  margin: 0 0 8px 0;
}

.tech-mobile-profile-role {
  font-size: 16px; 
  color: #000;
  margin: 0 0 20px 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 600;
}

.tech-mobile-profile-details {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-top: 20px;
}

.tech-mobile-profile-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid rgba(0,0,0,0.1);
}

.tech-mobile-profile-label {
  font-size: 14px;
  color: #000;
  font-weight: 600;
}

.tech-mobile-profile-value {
  font-size: 14px;
  color: #000;
  font-weight: 500;
}

/* ===== LOGOUT BUTTON MOBILE ===== */
.tech-mobile-logout-btn {
  background: #2678dc;/* Rouge pour déconnexion */
  color: white;
  border: none;
  padding: 15px;
  border-radius: 16px;
  font-size: 18px; /* Augmenté de 16px à 18px */
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  margin-top: 20px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 15px rgba(220, 38, 38, 0.3);
}

.tech-mobile-logout-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(220, 38, 38, 0.4);
}

/* ===== ANIMATIONS MOBILE ===== */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.tech-mobile-card,
.tech-mobile-stat-card,
.tech-mobile-intervention-item {
  animation: slideInUp 0.6s ease-out;
}

.tech-mobile-header,
.tech-mobile-nav {
  animation: fadeIn 0.8s ease-out;
}

/* ===== RESPONSIVE MOBILE ===== */
@media (max-width: 480px) {
  .tech-mobile-header {
    padding: 12px 15px;
  }

  .tech-mobile-content {
    padding: 15px 10px 100px 10px;
  }

  .tech-mobile-stats-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .tech-mobile-card,
  .tech-mobile-stat-card,
  .tech-mobile-intervention-item {
    padding: 15px;
    border-radius: 16px;
  }

  .tech-mobile-intervention-details {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .tech-mobile-intervention-actions {
    flex-direction: column;
    gap: 8px;
  }

  .tech-mobile-nav {
    padding: 8px 0;
  }

  .tech-mobile-nav-item {
    padding: 6px 8px;
    min-width: 50px;
  }

  .tech-mobile-nav-icon {
    font-size: 18px;
  }

  .tech-mobile-nav-label {
    font-size: 9px;
  }
}

/* ===== LANDSCAPE MOBILE ===== */
@media (max-height: 500px) and (orientation: landscape) {
  .tech-mobile-content {
    padding: 10px 15px 80px 15px;
  }

  .tech-mobile-nav {
    padding: 6px 0;
  }

  .tech-mobile-nav-item {
    padding: 4px 8px;
  }

  .tech-mobile-nav-icon {
    font-size: 16px;
  }

  .tech-mobile-nav-label {
    font-size: 8px;
  }
}

/* ===== DARK MODE SUPPORT ===== */
@media (prefers-color-scheme: dark) {
  .tech-dashboard-container {
    background:#fff;/* Violet plus foncé pour le dark mode */
  }

  .tech-mobile-header,
  .tech-mobile-nav {
    background:#fff;
  }

  .tech-mobile-card,
  .tech-mobile-stat-card,
  .tech-mobile-intervention-item,
  .tech-mobile-profile-card {
    background: #fff;
    border: 1px solid rgba(255,255,255,0.1);
  }

  .tech-mobile-card-title,
  .tech-mobile-intervention-client,
  .tech-mobile-stat-number,
  .tech-mobile-profile-name {
    color: #000;
  }

  .tech-mobile-logo-text {
    color: #000;
  }
}

/* ===== FORMULAIRES ===== */
.tech-mobile-form {
  padding: 20px;
}

.tech-mobile-form-group {
  margin-bottom: 20px;
}

.tech-mobile-form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #000;
  font-size: 16px; /* Augmenté de 14px à 16px */
}

.tech-mobile-form-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  font-size: 18px; /* Augmenté de 16px à 18px */
  background: white;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.tech-mobile-form-input:focus {
  outline: none;
  border-color: #3b82f6; /* Bleu pour le focus */
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.tech-mobile-form-input select {
  cursor: pointer;
}

/* ===== SCANNER QR ===== */
.tech-mobile-scanner-container {
  padding: 20px;
  text-align: center;
}

.tech-mobile-scanner-placeholder {
  padding: 40px 20px;
}

.tech-mobile-scanner-icon {
  font-size: 64px;
  margin-bottom: 20px;
  opacity: 0.7;
}

.tech-mobile-scanner-placeholder h3 {
  margin: 0 0 10px 0;
  color: #000;
  font-size: 20px;
}

.tech-mobile-scanner-placeholder p {
  margin: 0 0 20px 0;
  color: #000;
  font-size: 14px;
}

.tech-mobile-scanner-active {
  padding: 20px;
}

.tech-mobile-scanner-viewfinder {
  position: relative;
  width: 250px;
  height: 250px;
  margin: 0 auto 20px auto;
  background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
  border-radius: 20px;
  overflow: hidden;
}

.tech-mobile-scanner-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tech-mobile-scanner-frame {
  width: 180px;
  height: 180px;
  border: 3px solid #2678dc; /* Orange pour le scanner */
  border-radius: 15px;
  position: relative;
  animation: scannerPulse 2s infinite;
}

.tech-mobile-scanner-frame::before,
.tech-mobile-scanner-frame::after {
  content: '';
  position: absolute;
  width: 20px;
  height: 20px;
  border: 3px solid #2678dc; /* Orange pour les coins */
}

.tech-mobile-scanner-frame::before {
  top: -3px;
  left: -3px;
  border-right: none;
  border-bottom: none;
}

.tech-mobile-scanner-frame::after {
  bottom: -3px;
  right: -3px;
  border-left: none;
  border-top: none;
}

.tech-mobile-scanner-actions {
  display: flex;
  gap: 10px;
  justify-content: center;
  margin-top: 20px;
}

@keyframes scannerPulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
}

/* ===== CARTE ===== */
.tech-mobile-map-container {
  padding: 20px;
}

.tech-mobile-map-placeholder {
  text-align: center;
  padding: 20px;
}

.tech-mobile-map-icon {
  font-size: 64px;
  margin-bottom: 20px;
  opacity: 0.7;
}

.tech-mobile-map-placeholder h3 {
  margin: 0 0 10px 0;
  color: #000;
  font-size: 20px;
}

.tech-mobile-map-placeholder p {
  margin: 0 0 30px 0;
  color: #000;
  font-size: 16px;
}

.tech-mobile-map-clients {
  text-align: left;
}

.tech-mobile-map-client-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  margin-bottom: 10px;
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.tech-mobile-map-client-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.15); /* Ombre orange au hover */
}

.tech-mobile-map-client-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.tech-mobile-map-client-info strong {
  color: #000;
  font-size: 20px;
}

.tech-mobile-map-client-info span {
  color: #000;
  font-size: 20px;
}

/* ===== ANIMATIONS ===== */
@keyframes slideInUp {
  from {
    transform: translateY(30px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* ===== BOUTONS DE NAVIGATION ===== */
.tech-mobile-back-btn {
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid #3b82f6;
  color: #3b82f6;
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 16px; /* Augmenté de 14px à 16px */
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.tech-mobile-back-btn:hover {
  background: #3b82f6;
  color: white;
  transform: translateX(-2px);
}

.tech-mobile-refresh-btn {
  background: rgba(96, 165, 250, 0.1);
  border: 1px solid #60a5fa;
  color: #60a5fa;
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 18px; /* Augmenté de 16px à 18px */
  cursor: pointer;
  transition: all 0.3s ease;
}

.tech-mobile-refresh-btn:hover {
  background: #60a5fa;
  color: white;
  transform: rotate(180deg);
}

/* ===== RECHERCHE ===== */
.tech-mobile-search-container {
  position: relative;
  padding: 20px;
}

.tech-mobile-search-input {
  width: 100%;
  padding: 12px 16px 12px 45px;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  font-size: 18px; /* Augmenté de 16px à 18px */
  background: white;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.tech-mobile-search-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.tech-mobile-search-icon {
  position: absolute;
  left: 35px;
  top: 50%;
  transform: translateY(-50%);
  color: #000;
  font-size: 18px;
  pointer-events: none;
}

/* ===== ÉTATS DE CHARGEMENT ET ERREUR ===== */
.tech-mobile-loading {
  text-align: center;
  padding: 40px 20px;
}

.tech-mobile-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e5e7eb;
  border-top: 4px solid #2678dc;; /* Orange pour le spinner */
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.tech-mobile-loading p {
  color: #000;
  font-size: 16px;
  margin: 0;
}

.tech-mobile-error {
  text-align: center;
  padding: 40px 20px;
}

.tech-mobile-error-icon {
  font-size: 48px;
  margin-bottom: 20px;
}

.tech-mobile-error h3 {
  color: #2678dc;;
  margin: 0 0 10px 0;
  font-size: 20px;
}

.tech-mobile-error p {
  color: #000;
  margin: 0 0 20px 0;
  font-size: 14px;
}

/* ===== ÉTAT VIDE ===== */
.tech-mobile-empty-state {
  text-align: center;
  padding: 40px 20px;
}

.tech-mobile-empty-icon {
  font-size: 64px;
  margin-bottom: 20px;
  opacity: 0.5;
}

.tech-mobile-empty-state h3 {
  color: #000;
  margin: 0 0 10px 0;
  font-size: 20px;
}

.tech-mobile-empty-state p {
  color: #000;
  margin: 0;
  font-size: 14px;
}

/* ===== STYLES POUR LES PAGES SÉPARÉES ===== */

/* Filtres pour FacturesPage */
.tech-mobile-filter-container {
  display: flex;
  gap: 8px;
  padding: 10px 0;
  overflow-x: auto;
}

.tech-mobile-filter-btn {
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid #3b82f6;
  color: #3b82f6;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 16px; /* Augmenté de 14px à 16px */
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  min-width: fit-content;
}

.tech-mobile-filter-btn.active,
.tech-mobile-filter-btn:hover {
  background: #3b82f6;
  color: white;
  transform: translateY(-1px);
}

/* Scanner QR Code */
.tech-mobile-scanner-placeholder {
  text-align: center;
  padding: 40px 20px;
}

.tech-mobile-scanner-icon {
  font-size: 64px;
  margin-bottom: 20px;
}

.tech-mobile-scanner-placeholder h3 {
  color: #000;
  margin-bottom: 10px;
  font-size: 20px;
}

.tech-mobile-scanner-placeholder p {
  color: #000;
  margin-bottom: 30px;
  line-height: 1.5;
}

.tech-mobile-scanner-active {
  padding: 20px;
  text-align: center;
}

.tech-mobile-scanner-viewfinder {
  position: relative;
  width: 250px;
  height: 250px;
  margin: 0 auto 20px;
  background: #f3f4f6;
  border-radius: 12px;
  overflow: hidden;
}

.tech-mobile-scanner-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg,
    rgba(99, 102, 241, 0.1) 25%,
    transparent 25%,
    transparent 75%,
    rgba(99, 102, 241, 0.1) 75%
  );
  background-size: 20px 20px;
  animation: scannerAnimation 2s linear infinite;
}

.tech-mobile-scanner-frame {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 180px;
  height: 180px;
  border: 3px solid #2678dc;; /* Orange pour le scanner */
  border-radius: 12px;
}

.tech-mobile-scanner-frame::before {
  content: '';
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  height: 3px;
  background: #2678dc; /* Orange pour la ligne de scan */
  animation: scanLine 2s ease-in-out infinite;
}

@keyframes scannerAnimation {
  0% { background-position: 0 0; }
  100% { background-position: 20px 20px; }
}

@keyframes scanLine {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(180px); }
}

.tech-mobile-scanner-actions {
  display: flex;
  justify-content: center;
  gap: 15px;
}

/* Formulaires */
.tech-mobile-form {
  padding: 20px;
}

.tech-mobile-form-group {
  margin-bottom: 20px;
}

.tech-mobile-form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #000;
  font-size: 20px;
}

.tech-mobile-form-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 16px;
  transition: all 0.3s ease;
  background: white;
  box-sizing: border-box;
}

.tech-mobile-form-input:focus {
  outline: none;
  border-color: #F59E0B; /* Orange pour le focus */
  box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.1);
}

.tech-mobile-form-input::placeholder {
  color: #000;
}

/* Champs de profil en lecture seule */
.tech-mobile-profile-field {
  padding: 12px 16px;
  background: #f9fafb;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  color: #000;
  font-size: 16px;
}

/* États de chargement et erreur */
.tech-mobile-loading {
  text-align: center;
  padding: 40px 20px;
}

.tech-mobile-error {
  text-align: center;
  padding: 40px 20px;
  color: #2678dc;
}

.tech-mobile-error-icon {
  font-size: 48px;
  margin-bottom: 15px;
}

/* Responsive pour les nouvelles pages */
@media (max-width: 480px) {
  .tech-mobile-filter-container {
    padding: 10px 5px;
  }

  .tech-mobile-filter-btn {
    padding: 6px 12px;
    font-size: 22px;
  }

  .tech-mobile-scanner-viewfinder {
    width: 200px;
    height: 200px;
  }

  .tech-mobile-scanner-frame {
    width: 140px;
    height: 140px;
  }

  .tech-mobile-form {
    padding: 15px;
  }

  .tech-mobile-form-input {
    padding: 10px 14px;
    font-size: 22px;
  }

  .tech-mobile-profile-field {
    padding: 10px 14px;
    font-size: 22px;
  }
}

/* ===== ACTIONS RAPIDES (OVERVIEW) ===== */
.tech-mobile-quick-actions {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  padding: 20px;
}

.tech-mobile-quick-action {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 15px;
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
}

.tech-mobile-quick-action:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.tech-mobile-quick-action-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 22px;
  color: white;
  flex-shrink: 0;
}

.tech-mobile-quick-action-content {
  flex: 1;
  min-width: 0;
}

.tech-mobile-quick-action-title {
  font-weight: 600;
  color: #000;
  font-size: 22px;
  margin-bottom: 2px;
}

.tech-mobile-quick-action-desc {
  font-size: 22px;
  color: #000;
  line-height: 1.3;
}

@media (max-width: 480px) {
  .tech-mobile-quick-actions {
    grid-template-columns: 1fr;
    gap: 12px;
    padding: 15px;
  }

  .tech-mobile-quick-action {
    padding: 12px;
  }

  .tech-mobile-quick-action-icon {
    width: 35px;
    height: 35px;
    font-size: 16px;
  }

  .tech-mobile-quick-action-title {
    font-size: 13px;
  }

  .tech-mobile-quick-action-desc {
    font-size: 11px;
  }
}

/* ===== STYLES POUR LA LISTE DES CLIENTS ===== */
.clients-list-container {
  padding: 20px;
  max-height: calc(100vh - 200px);
  overflow-y: auto;
}

.clients-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.client-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  border: 1px solid rgba(99, 102, 241, 0.1);
  transition: all 0.3s ease;
}

.client-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(99, 102, 241, 0.15);
  border-color: rgba(99, 102, 241, 0.3);
}

.client-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #f0f0f0;
}

.client-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 20px; /* Augmenté de 18px à 20px */
  font-weight: 600;
}

.client-id {
  background: linear-gradient(45deg, #60a5fa, #3b82f6); /* Bleu pour l'ID client */
  color: white;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 14px; /* Augmenté de 12px à 14px */
  font-weight: 500;
}

.client-details {
  margin-bottom: 15px;
}

.client-details p {
  margin: 8px 0;
  color: #555;
  font-size: 14px;
  line-height: 1.4;
}

.client-details strong {
  color: #2c3e50;
}

.client-actions {
  display: flex;
  justify-content: flex-end;
}

.select-client-btn {
  background: linear-gradient(45deg, #3b82f6, #1d4ed8); /* Bleu pour sélectionner */
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  font-size: 16px; /* Augmenté de 14px à 16px */
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.select-client-btn:hover {
  background: linear-gradient(45deg, #1e40af, #1d4ed8); /* Bleu plus foncé au hover */
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.4);
}

.clients-summary {
  background: rgba(99, 102, 241, 0.1);
  padding: 15px;
  border-radius: 8px;
  text-align: center;
  margin-top: 20px;
}

.clients-summary p {
  margin: 0;
  color: #2D5A27; /* Vert pour le résumé */
  font-weight: 500;
}

.no-data-message {
  text-align: center;
  padding: 40px 20px;
  color: #666;
}

.no-data-message p {
  margin-bottom: 20px;
  font-size: 16px;
}

.retry-btn {
  background: linear-gradient(45deg, #60a5fa, #3b82f6); /* Bleu clair pour réessayer */
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px; /* Augmenté de 14px à 16px */
  cursor: pointer;
  transition: all 0.3s ease;
}

.retry-btn:hover {
  background: linear-gradient(45deg, #d97706, #F59E0B); /* Orange plus foncé au hover */
  transform: translateY(-1px);
}

/* Responsive pour mobile */
@media (max-width: 768px) {
  .clients-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .client-card {
    padding: 15px;
  }

  .client-header h3 {
    font-size: 16px;
  }

  .client-details p {
    font-size: 13px;
  }
}
